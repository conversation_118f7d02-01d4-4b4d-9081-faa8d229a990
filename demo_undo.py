# -*-coding:utf-8-*-
"""
撤销功能演示脚本

快速启动Web版图像标注工具，并展示撤销功能的使用方法。
"""

import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def demo_undo():
    """演示撤销功能"""
    print("🔄 图像标注工具 - 撤销功能演示")
    print("=" * 60)
    
    print("📋 撤销功能演示步骤：")
    print()
    print("1️⃣ 启动Web服务，自动打开浏览器")
    print("2️⃣ 在网页中选择几个样本（单击卡片选择）") 
    print("3️⃣ 使用键盘快捷键或按钮进行标注（如按 '1' 键）")
    print("4️⃣ 观察界面顶部出现蓝色撤销提示")
    print("5️⃣ 按 Ctrl+Z 键触发撤销操作")
    print("6️⃣ 确认样本重新出现并保持选中状态")
    
    print("\n⌨️ 快捷键说明：")
    shortcuts = [
        ("标注快捷键", "0-4, 9, b (移动到对应标签), d (删除)"),
        ("撤销快捷键", "Ctrl+Z (Windows/Linux), Cmd+Z (macOS)"),
        ("选择快捷键", "单击样本卡片选择，支持多选"),
        ("视频播放", "双击样本卡片播放视频")
    ]
    
    for shortcut, desc in shortcuts:
        print(f"  {shortcut:12s}: {desc}")
    
    print("\n🎯 撤销功能特点：")
    features = [
        "✅ 智能记录：只记录标注操作，不记录删除操作",
        "✅ 保持选中：撤销后样本自动保持选中状态", 
        "✅ 文件还原：从标签目录精确移回原位置",
        "✅ 日志同步：自动清理撤销操作的日志记录",
        "✅ 状态恢复：样本状态从'已处理'变回'未处理'",
        "✅ 界面提示：撤销按钮和状态实时显示"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n⚠️ 注意事项：")
    notes = [
        "删除操作（d键）不可撤销，请谨慎使用",
        "只能撤销最近一次的标注操作",
        "撤销后建议立即重新标注正确的标签",
        "大量标注前建议先测试撤销功能"
    ]
    
    for note in notes:
        print(f"  • {note}")
    
    start_demo = input("\n🚀 按回车键启动演示 (或输入 'q' 退出): ").strip()
    
    if start_demo.lower() == 'q':
        return
    
    print("\n正在启动Web服务...")
    
    # 检查脚本是否存在
    web_script = Path(__file__).parent / 'Check_imgs_Web.py'
    if not web_script.exists():
        print("❌ 找不到 Check_imgs_Web.py 文件")
        return
    
    try:
        # 启动Web服务
        print("🌐 Web服务启动成功！")
        print("📝 请按照演示步骤测试撤销功能")
        print("🔄 特别注意撤销后样本的选中状态")
        print("\n按 Ctrl+C 停止演示")
        
        subprocess.run(['python', str(web_script)], check=True)
        
    except KeyboardInterrupt:
        print("\n✅ 演示结束！")
        print("🎉 感谢体验撤销功能")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        print("请检查Python环境和依赖包是否正确安装")

if __name__ == "__main__":
    demo_undo()

# -*-coding:utf-8-*-
"""
图像标注工具 - Web版演示脚本

这个脚本用于快速启动Web版图像标注工具的演示。
它会使用默认的samples目录，并自动打开浏览器展示功能。
"""

import os
import sys
from pathlib import Path

def main():
    """主函数"""
    print("图像标注工具 - Web版演示")
    print("=" * 50)
    
    # 检查当前目录结构
    current_dir = Path(__file__).parent
    samples_dir = current_dir / 'samples'
    
    if not samples_dir.exists():
        print("错误: 找不到samples目录")
        print(f"请确保 {samples_dir} 目录存在并包含样本文件夹")
        return
    
    print(f"样本目录: {samples_dir}")
    
    # 统计样本文件夹数量
    sample_count = 0
    for item in samples_dir.rglob('*'):
        if item.is_dir():
            # 简单检查是否包含必要的文件类型
            files = list(item.iterdir())
            pkl_files = [f for f in files if f.suffix == '.pkl']
            avi_files = [f for f in files if f.suffix == '.avi']
            jpg_files = [f for f in files if f.suffix == '.jpg']
            
            if len(pkl_files) == 1 and len(avi_files) == 1 and len(jpg_files) == 3:
                sample_count += 1
    
    print(f"发现 {sample_count} 个样本文件夹")
    
    if sample_count == 0:
        print("警告: 没有找到有效的样本文件夹")
        print("样本文件夹应包含: 1个.pkl文件、1个.avi文件、3个.jpg文件")
        response = input("是否继续启动Web服务? (y/n): ").lower().strip()
        if response != 'y':
            return
    
    print("\n启动说明:")
    print("1. Web服务启动后会自动打开浏览器")
    print("2. 在网页中可以看到所有拼接后的样本图片")
    print("3. 点击图片可以选择/取消选择")
    print("4. 选择样本后点击标签按钮进行批量标注")
    print("5. 支持键盘快捷键 0-4, 9, b, d")
    print("6. 按 Ctrl+C 停止服务")
    
    response = input("\n按回车键启动Web服务 (或输入 'q' 退出): ").strip()
    if response.lower() == 'q':
        return
    
    print("\n正在启动Web服务...")
    
    # 导入并运行Web版本
    try:
        from Check_imgs_Web import main as web_main, initialize_config
        
        # 使用默认配置
        initialize_config(samples_dir)
        web_main()
        
    except ImportError as e:
        print(f"错误: 无法导入Web版本模块: {e}")
        print("请确保 Check_imgs_Web.py 文件存在")
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")


if __name__ == "__main__":
    main()

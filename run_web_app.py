#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
启动脚本 - 图像标注工具Web版
Created by @Moss 2025.08.22

这是一个启动脚本，用于运行图像标注工具的Web版本。
通过这种方式可以避免PyCharm自动识别为Flask项目。

使用方法：
1. 在PyCharm中右键运行此文件
2. 或在终端中运行：python run_web_app.py
"""

import sys
import os
from pathlib import Path

def main():
    """主启动函数"""
    print("🚀 启动图像标注工具 - Web版")
    print("=" * 50)
    
    # 确保当前目录在Python路径中
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    try:
        # 动态导入主模块
        print("📦 正在加载主程序模块...")
        import Check_imgs_Web
        
        # 运行主程序
        print("✅ 模块加载成功，启动Web应用...")
        Check_imgs_Web.main()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保Check_imgs_Web.py文件存在于当前目录")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

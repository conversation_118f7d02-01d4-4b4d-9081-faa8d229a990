#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Flask应用创建
"""

def test_simple_flask():
    """测试简单的Flask应用创建"""
    try:
        from flask import Flask, render_template, request, jsonify, send_from_directory
        
        print("🔍 创建Flask应用...")
        app = Flask(__name__)
        
        @app.route('/')
        def index():
            return "Hello World"
        
        print(f"✅ Flask应用创建成功: {type(app)}")
        print(f"   应用名称: {app.name}")
        return app
        
    except Exception as e:
        print(f"❌ Flask应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_create_web_app_inline():
    """内联测试create_web_app的核心逻辑"""
    try:
        from flask import Flask, render_template, request, jsonify, send_from_directory
        
        print("🔍 内联创建Web应用...")
        app = Flask(__name__)
        
        @app.route('/')
        def index():
            """主页"""
            return "Test Page"
        
        @app.route('/api/test')
        def test():
            """测试API"""
            return jsonify({'success': True, 'message': 'Test OK'})
        
        print(f"✅ 内联Web应用创建成功: {type(app)}")
        print(f"   应用名称: {app.name}")
        print(f"   路由数量: {len(app.url_map._rules)}")
        
        return app
        
    except Exception as e:
        print(f"❌ 内联Web应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("🔍 开始简单Flask测试")
    print("=" * 50)
    
    # 测试1：简单Flask应用
    app1 = test_simple_flask()
    if app1 is None:
        print("❌ 简单Flask测试失败")
        return
    
    print()
    
    # 测试2：内联create_web_app逻辑
    app2 = test_create_web_app_inline()
    if app2 is None:
        print("❌ 内联Web应用测试失败")
        return
    
    print("=" * 50)
    print("✅ 所有测试通过！")

if __name__ == "__main__":
    main()


<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SitUp图组标注工具 - Moss版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .label-buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .samples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .sample-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .sample-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .sample-card.selected {
            border: 3px solid #007bff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .sample-card.processed {
            opacity: 0.6;
            border: 2px solid #28a745;
        }
        .sample-card.deleted {
            opacity: 0.4;
            border: 2px solid #dc3545;
        }
        .sample-image {
            width: 100%;
            height: 400px;
            object-fit: contain;
            background-color: #f8f9fa;
            transition: transform 0.2s ease;
        }
        .sample-card:hover .sample-image {
            transform: scale(1.1);
        }
        .sample-info {
            padding: 15px;
        }
        .sample-name {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 12px;
            word-break: break-all;
        }
        .sample-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        .status-unprocessed {
            background-color: #e9ecef;
            color: #495057;
        }
        .status-processed {
            background-color: #d4edda;
            color: #155724;
        }
        .status-deleted {
            background-color: #f8d7da;
            color: #721c24;
        }
        .selection-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        /* 固定右上角的消息容器 */
        #message-container {
            position: fixed;
            top: 16px;
            right: 16px;
            z-index: 2000;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
            pointer-events: none; /* 允许下方元素交互 */
            max-width: min(420px, 90vw);
        }
        #message-container .message {
            pointer-events: auto; /* 消息本身可选中/复制 */
            margin: 0; /* 由容器gap控制间距 */
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        @media (max-width: 480px) {
            #message-container {
                top: 8px;
                right: 8px;
                max-width: 92vw;
            }
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .message {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .sample-image {
            cursor: pointer;
        }
        .sample-card {
            user-select: none; /* 防止选择文本 */
        }
        .sample-card:active {
            transform: scale(0.98);
        }

        /* 内联标签按钮样式 */
        .sample-label-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 3px;
            margin-top: 8px;
            padding: 6px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 6px;
            border-top: 1px solid #e9ecef;
            align-items: center;
        }

        .label-btn-small {
            padding: 2px 6px;
            font-size: 10px;
            font-weight: bold;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 18px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .label-btn-small.primary {
            background: #007bff;
            color: white;
        }

        .label-btn-small.primary:hover {
            background: #0056b3;
            transform: scale(1.1);
        }

        .label-btn-small.warning {
            background: #ffc107;
            color: #212529;
        }

        .label-btn-small.warning:hover {
            background: #e0a800;
            transform: scale(1.1);
        }

        .label-btn-small.danger {
            background: #dc3545;
            color: white;
        }

        .label-btn-small.danger:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        /* 删除按钮居右 */
        .label-btn-small.delete-btn {
            margin-left: auto;
        }

        /* 统计信息美化 */
        #sample-stats {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 15px;
            font-size: 14px;
            line-height: 1.6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .stats-label {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            margin-right: 4px;
            font-weight: bold;
        }

        .stats-count {
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>SitUp图组标注工具 - Moss版</h1>
        <div id="sample-stats" style="margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 14px; color: #6c757d;">
            正在加载样本统计信息...
        </div>
        <div id="progress-container" style="margin-bottom: 10px; display: none;">
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                    <span id="progress-text">正在生成拼接图片...</span>
                    <span id="progress-percentage">0%</span>
                </div>
                <div style="background: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden;">
                    <div id="progress-bar" style="background: #007bff; height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
            </div>
        </div>
        <div class="controls">
            <button class="btn btn-primary" onclick="refreshSamples()">刷新样本</button>
            <button class="btn btn-primary" onclick="selectAll()">全选</button>
            <button class="btn btn-primary" onclick="clearSelection()">清除选择</button>
            <div class="selection-info">
                已选择: <span id="selected-count">0</span> 个样本
            </div>
        </div>
        <div class="controls" style="margin-top: 10px;">
            <span>标签操作:</span>
            <div class="label-buttons">
                <button class="btn btn-success" onclick="labelSelected('0')">标签 0</button>
                <button class="btn btn-success" onclick="labelSelected('1')">标签 1</button>
                <button class="btn btn-success" onclick="labelSelected('2')">标签 2</button>
                <button class="btn btn-success" onclick="labelSelected('3')">标签 3</button>
                <button class="btn btn-success" onclick="labelSelected('4')">标签 4</button>
                <button class="btn btn-success" onclick="labelSelected('5')">标签 5</button>
                <button class="btn btn-success" onclick="labelSelected('6')">标签 6</button>
                <button class="btn btn-success" onclick="labelSelected('7')">标签 7</button>
                <button class="btn btn-success" onclick="labelSelected('8')">标签 8</button>
                <button class="btn btn-success" onclick="labelSelected('9')">标签 9</button>
                <button class="btn btn-warning" onclick="labelSelected('b')">标签 b</button>
                <button class="btn btn-danger" onclick="labelSelected('d')">删除</button>
            </div>
        </div>
        <div class="controls" style="margin-top: 10px;">
            <div style="background: #e9ecef; padding: 10px; border-radius: 4px; font-size: 14px;">
                <strong>操作说明:</strong> 单击样本卡片选择/取消选择，双击播放视频，<strong>点击样本下方标签按钮直接标注</strong>，支持键盘快捷键 0-9、b、d，<strong>Ctrl+Z 撤销</strong>
            </div>
        </div>
        <div id="undo-status" style="margin-top: 10px; display: none;">
            <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 10px; font-size: 14px; color: #0c5460;">
                <span id="undo-info">可以撤销最近的标注操作</span>
                <button class="btn btn-sm" onclick="undoLastOperation()" style="margin-left: 10px; padding: 4px 8px; font-size: 12px; background: #17a2b8; color: white;">
                    撤销 (Ctrl+Z)
                </button>
            </div>
        </div>
    </div>

    <div id="message-container"></div>
    <div id="samples-container" class="loading">
        正在加载样本...
    </div>





    <script>
        let samples = [];
        let selectedSamples = new Set();
        let progressInterval = null;

        async function loadSamples(preserveSelection = false) {
            try {
                const response = await fetch('/api/samples');
                const data = await response.json();

                if (data.success) {
                    samples = data.samples;

                    // 更新统计信息
                    if (data.label_stats) {
                        updateStatistics(data.label_stats);
                    }

                    // 如果不保留选中状态，则清除选中
                    if (!preserveSelection) {
                        // 正常加载，可能需要监控进度
                        renderSamples();
                        startProgressMonitoring();
                    } else {
                        // 保留选中状态，只渲染样本
                        renderSamples();
                    }

                    checkUndoStatus(); // 检查是否有可撤销的操作
                } else {
                    showMessage('加载样本失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        function startProgressMonitoring() {
            // 开始监控处理进度
            progressInterval = setInterval(async () => {
                try {
                    const response = await fetch('/api/processing_status');
                    const data = await response.json();

                    if (data.success) {
                        updateProgress(data.stats);

                        // 更新统计信息
                        if (data.label_stats) {
                            updateStatistics(data.label_stats);
                        }

                        // 更新样本数据（有些图片可能已经生成完成）
                        if (JSON.stringify(data.samples) !== JSON.stringify(samples)) {
                            samples = data.samples;
                            renderSamples();
                        }

                        // 如果处理完成，停止监控
                        if (!data.stats.in_progress && data.stats.completed === data.stats.total) {
                            clearInterval(progressInterval);
                            hideProgress();
                        }
                    }
                } catch (error) {
                    console.error('获取进度失败:', error);
                }
            }, 1000); // 每秒更新一次
        }

        function updateProgress(stats) {
            if (stats.total === 0) return;

            const percentage = Math.round((stats.completed / stats.total) * 100);
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const progressPercentage = document.getElementById('progress-percentage');

            if (stats.in_progress || stats.completed < stats.total) {
                progressContainer.style.display = 'block';
                progressBar.style.width = percentage + '%';
                progressText.textContent = `正在生成拼接图片 (${stats.completed}/${stats.total})`;
                progressPercentage.textContent = percentage + '%';
            }
        }

        function hideProgress() {
            const progressContainer = document.getElementById('progress-container');
            progressContainer.style.display = 'none';
            showMessage('所有图片生成完成！', 'success');
        }

        function renderSamples() {
            const container = document.getElementById('samples-container');

            if (samples.length === 0) {
                container.innerHTML = '<div class="loading">没有找到样本文件夹</div>';
                return;
            }

            // 过滤掉已处理和已删除的样本，只显示未处理的
            const unprocessedSamples = samples.filter(sample => sample.status === 'unprocessed');

            if (unprocessedSamples.length === 0) {
                container.innerHTML = '<div class="loading">所有样本都已处理完成！</div>';
                return;
            }

            container.innerHTML = '<div class="samples-grid">' + unprocessedSamples.map(sample => `
                <div class="sample-card ${sample.status} ${selectedSamples.has(sample.id) ? 'selected' : ''}"
                     data-sample-id="${sample.id}"
                     onclick="toggleSelect(${sample.id})"
                     ondblclick="playVideo(${sample.id})"
                     title="单击选择，双击播放视频">
                    ${sample.image_generated && sample.concatenated_image ?
                        `<img src="/static/${sample.concatenated_image}" alt="${sample.name}" class="sample-image" />` :
                        `<div class="sample-image" style="display: flex; align-items: center; justify-content: center; background: #f8f9fa; color: #6c757d;">
                            <div style="text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 10px;">📷</div>
                                <div>正在生成图片...</div>
                            </div>
                         </div>`
                    }
                    <div class="sample-info">
                        <div class="sample-name">${sample.name}</div>
                        ${sample.status !== 'unprocessed' ? `
                            <span class="sample-status status-${sample.status}">
                                ${sample.status === 'processed' ? '已处理 (' + (sample.label || '') + ')' : '已删除'}
                            </span>
                        ` : ''}
                        ${sample.status === 'unprocessed' ? `
                            <div class="sample-label-buttons">
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '0')" title="标签 0">0</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '1')" title="标签 1">1</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '2')" title="标签 2">2</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '3')" title="标签 3">3</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '4')" title="标签 4">4</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '5')" title="标签 5">5</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '6')" title="标签 6">6</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '7')" title="标签 7">7</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '8')" title="标签 8">8</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '9')" title="标签 9">9</button>
                                <button class="label-btn-small warning" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, 'b')" title="标签 b">b</button>
                                <button class="label-btn-small danger delete-btn" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, 'd')" title="删除">×</button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('') + '</div>';

            updateSelectedCount();
        }

        function toggleSelect(sampleId) {
            const sample = samples.find(s => s.id === sampleId);
            if (sample && sample.status === 'unprocessed') {
                if (selectedSamples.has(sampleId)) {
                    selectedSamples.delete(sampleId);
                } else {
                    selectedSamples.add(sampleId);
                }
                renderSamples();
            }
        }

        function selectAll() {
            selectedSamples.clear();
            samples.forEach(sample => {
                if (sample.status === 'unprocessed') {
                    selectedSamples.add(sample.id);
                }
            });
            renderSamples();
        }

        function clearSelection() {
            selectedSamples.clear();
            renderSamples();
        }

        function updateStatistics(stats) {
            // 更新选中数量
            document.getElementById('selected-count').textContent = selectedSamples.size;

            // 更新基础统计信息
            const statsElement = document.getElementById('sample-stats');
            if (statsElement && stats) {
                let statsHtml = `
                    <div style="margin-bottom: 8px;">
                        <span class="stats-count">总样本: ${stats.total}</span> |
                        <span class="stats-count">未处理: ${stats.unprocessed}</span> |
                        <span class="stats-count">已处理: ${stats.processed}</span> |
                        <span class="stats-count">已删除: ${stats.deleted}</span>
                    </div>
                `;

                // 添加标签统计
                if (stats.labels && Object.keys(stats.labels).length > 0) {
                    const labelStats = [];

                    // 按标签排序 (0-9, b)
                    const sortedLabels = Object.keys(stats.labels).sort((a, b) => {
                        if (a === 'b') return 1;
                        if (b === 'b') return -1;
                        return parseInt(a) - parseInt(b);
                    });

                    for (const label of sortedLabels) {
                        const count = stats.labels[label];
                        if (count > 0) {
                            labelStats.push(`<span class="stats-label">${label}</span><span class="stats-count">${count}</span>`);
                        }
                    }

                    if (labelStats.length > 0) {
                        statsHtml += `
                            <div style="color: #666; font-size: 13px;">
                                <strong>标签分布:</strong> ${labelStats.join(' ')}
                            </div>
                        `;
                    }
                }

                statsElement.innerHTML = statsHtml;
            }
        }

        function updateSelectedCount() {
            // 保持向后兼容的函数，用于不传入stats参数的情况
            const unprocessedCount = samples.filter(sample => sample.status === 'unprocessed').length;
            const processedCount = samples.filter(sample => sample.status === 'processed').length;
            const deletedCount = samples.filter(sample => sample.status === 'deleted').length;

            // 计算标签统计
            const labelCounts = {};
            samples.filter(sample => sample.status === 'processed').forEach(sample => {
                if (sample.label) {
                    labelCounts[sample.label] = (labelCounts[sample.label] || 0) + 1;
                }
            });

            const stats = {
                total: samples.length,
                unprocessed: unprocessedCount,
                processed: processedCount,
                deleted: deletedCount,
                labels: labelCounts
            };

            updateStatistics(stats);
        }

        async function labelSelected(label) {
            if (selectedSamples.size === 0) {
                showMessage('请先选择要标注的样本', 'error');
                return;
            }

            const confirmed = confirm(`确认将 ${selectedSamples.size} 个样本标记为 "${label}"?`);
            if (!confirmed) return;

            try {
                const response = await fetch('/api/label', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sample_ids: Array.from(selectedSamples),
                        label: label
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const successCount = data.results.filter(r => r.success).length;
                    showMessage(`成功处理 ${successCount} 个样本`, 'success');
                    selectedSamples.clear();
                    await loadSamples(); // 重新加载样本
                } else {
                    showMessage('批量标注失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        async function refreshSamples() {
            showMessage('正在重新扫描样本...', 'success');
            try {
                const response = await fetch('/api/refresh');
                const data = await response.json();

                if (data.success) {
                    samples = data.samples;
                    selectedSamples.clear();
                    renderSamples();

                    // 如果有标签统计，更新统计信息
                    if (data.label_stats) {
                        updateStatistics(data.label_stats);
                    } else {
                        updateSelectedCount(); // 回退到计算统计
                    }

                    showMessage(data.message, 'success');
                } else {
                    showMessage('刷新失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        function showMessage(message, type) {
            const container = document.getElementById('message-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            container.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSamples();
            setupDoubleClickHandler();
            setupUndoShortcut(); // 设置撤销快捷键
            setupContextMenu(); // 右键打开样本所在文件夹
        });

        // 键盘快捷键
        document.addEventListener('keypress', function(event) {
            if (selectedSamples.size > 0) {
                const key = event.key;
                if (['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b', 'd'].includes(key)) {
                    event.preventDefault();
                    labelSelected(key);
                }
            }
        });

        // 播放视频功能
        async function playVideo(sampleId) {
            try {
                console.log('开始播放视频，样本ID:', sampleId);
                showMessage('正在打开视频...', 'success');

                const response = await fetch('/api/play_video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sample_id: sampleId
                    })
                });

                console.log('API响应状态:', response.status);
                const data = await response.json();
                console.log('API响应数据:', data);

                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage('播放失败: ' + data.message, 'error');
                    console.error('播放失败详情:', data);
                }
            } catch (error) {
                console.error('播放视频出错:', error);
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 加强双击事件处理
        function setupDoubleClickHandler() {
            document.addEventListener('dblclick', function(event) {
                const sampleCard = event.target.closest('.sample-card');
                if (sampleCard) {
                    const sampleId = parseInt(sampleCard.getAttribute('data-sample-id'));
                    console.log('检测到双击事件，样本ID:', sampleId);
                    if (sampleId) {
                        event.preventDefault();
                        event.stopPropagation();
                        playVideo(sampleId);
                    }
                }
            });
        }

        // 检查撤销状态
        async function checkUndoStatus() {
            try {
                const response = await fetch('/api/can_undo');
                const data = await response.json();

                if (data.success) {
                    const undoStatus = document.getElementById('undo-status');
                    const undoInfo = document.getElementById('undo-info');

                    if (data.can_undo && data.operation_info) {
                        const info = data.operation_info;
                        undoInfo.textContent = `可以撤销 ${info.count} 个样本的标注 (标签: ${info.label})`;
                        undoStatus.style.display = 'block';
                    } else {
                        undoStatus.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('检查撤销状态失败:', error);
            }
        }

        // 撤销最近的操作
        async function undoLastOperation() {
            try {
                console.log('开始撤销操作...');
                showMessage('正在撤销...', 'success');

                const response = await fetch('/api/undo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                console.log('撤销API响应:', data);

                if (data.success) {
                    showMessage(data.message, 'success');

                    // 撤销成功后，重新选中被撤销的样本
                    if (data.undone_sample_ids && data.undone_sample_ids.length > 0) {
                        selectedSamples.clear();
                        data.undone_sample_ids.forEach(sampleId => {
                            selectedSamples.add(sampleId);
                        });
                        console.log('重新选中撤销的样本:', Array.from(selectedSamples));
                    }

                    // 重新加载样本数据（保持选中状态）
                    await loadSamples(true);

                } else {
                    showMessage('撤销失败: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('撤销操作出错:', error);
                showMessage('撤销失败: ' + error.message, 'error');
            }
        }

        // 设置Ctrl+Z快捷键
        function setupUndoShortcut() {
            document.addEventListener('keydown', function(event) {
                // 检测 Ctrl+Z (Windows/Linux) 或 Cmd+Z (Mac)
                if ((event.ctrlKey || event.metaKey) && event.key === 'z') {
                    event.preventDefault();
                    console.log('检测到Ctrl+Z快捷键');
                    undoLastOperation();
                }
            });
        }



        // 单个样本标注函数
        async function labelSingleSample(sampleId, label) {
            try {
                console.log(`内联标注单个样本: ${sampleId} -> ${label}`);

                // 直接执行标注，无需确认
                const response = await fetch('/api/label', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sample_ids: [sampleId],
                        label: label
                    })
                });

                const data = await response.json();

                if (data.success && data.results[0] && data.results[0].success) {
                    showMessage(`样本已标记为 "${label}"`, 'success');
                    await loadSamples(true); // 重新加载样本，保留选中状态
                    checkUndoStatus(); // 检查撤销状态
                } else {
                    const errorMsg = data.results && data.results[0] ? data.results[0].message : data.message;
                    showMessage('标注失败: ' + errorMsg, 'error');
                }
            } catch (error) {
                console.error('内联标注失败:', error);
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 右键菜单：打开样本所在文件夹
        function setupContextMenu() {
            document.addEventListener('contextmenu', function(event) {
                const sampleCard = event.target.closest('.sample-card');
                if (sampleCard) {
                    event.preventDefault();
                    const sampleId = parseInt(sampleCard.getAttribute('data-sample-id'));
                    if (sampleId) {
                        openSampleFolder(sampleId);
                    }
                }
            });
        }

        async function openSampleFolder(sampleId) {
            try {
                const response = await fetch('/api/open_folder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ sample_id: sampleId })
                });
                const data = await response.json();
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage('打开失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>

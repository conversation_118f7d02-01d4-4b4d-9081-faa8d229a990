需求: 
原脚本功能: 
脚本执行后，会依次在ubuntu18.04的图形界面打开Ai文件夹，逐个打开jpg文件，
等待我的标签输入，例如我输入1,则将该jpg文件和同名的pkl文件移动到1文件夹，
然后继续打开下一个jpg文件，直到该Ai文件夹中的所有jpg文件都被我全部执行完成，则继续打开Ai+1文件夹，
依次循环；

将原脚本功能修改为:
【样本文件夹】：指给出路径中仅包含1个pkl，1个avi，3个jpg的文件夹叫做1个样本文件夹

1. 脚本执行后，会依次在ubuntu系统的图形界面打开样本文件夹，将【样本文件夹】中的所有jpg纵向拼接起来；
(0) 参考样本文件夹: /media/pyl/WD_Blue_1T/All_proj/yskj_proj/Check_Methods/samples/scl2/2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17
(1) 具体拼接方法如下: 
例如: 3个jpg文件名: 
2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17-1096.jpg
2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17-1128.jpg
2024_12_13_14_6_17_192.168.2.210_1_3_A__len35_loop1096_1151_17-1151.jpg
按照文件名末尾的数字大小拼接: 1096,1128，1151 , 1096在最上，1128在最下

2. 等待我的标签输入，例如我输入1，将该【样本文件夹】整体移动到1文件夹中；
3. 然后继续打开下1个【样本文件夹】


重要要求:
上述需求的修改，创建新文件Check_imgs.py完成
完成上述需求后，测试win系统的兼容性，将win的兼容版本，创建新文件Check_imgs_Win.py 完成


需求2:
将需求1中完成的脚本实现的功能进行可视化:
（1）在本地网页中，将拼接后展示的部分可视化在网页中；
（2）将原来1次只能拼接和查看1张图片，调整为在网页中拼接和查看多个样本文件夹；
（3）在网页中可以选中多个拼接后的样本，然后按键1次，将这些样本移动到标签位置；

重要要求:
上述修改，仍然是通过创建新文件完成；
能够兼容win10系统

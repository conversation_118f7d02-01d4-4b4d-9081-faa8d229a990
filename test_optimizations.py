# -*-coding:utf-8-*-
"""
测试Web版本优化功能的脚本

测试内容：
1. 已处理/已删除样本隐藏功能
2. 大量样本时的加载性能优化（懒加载）
3. Ubuntu双击播放视频功能（增强版）
4. 进度指示器功能
"""

import subprocess
import time
from pathlib import Path
import platform

def test_optimizations():
    """测试优化功能"""
    print("图像标注工具 - Web版 优化功能测试")
    print("=" * 60)
    
    # 检查系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {platform.python_version()}")
    
    # 检查当前目录
    current_dir = Path(__file__).parent
    web_script = current_dir / 'Check_imgs_Web.py'
    samples_dir = current_dir / 'samples'
    
    if not web_script.exists():
        print("❌ 找不到 Check_imgs_Web.py")
        return
    
    if not samples_dir.exists():
        print("❌ 找不到 samples 目录")
        return
    
    # 统计样本数量
    total_samples = 0
    for item in samples_dir.rglob('*'):
        if item.is_dir():
            files = list(item.iterdir())
            pkl_files = [f for f in files if f.suffix == '.pkl']
            avi_files = [f for f in files if f.suffix == '.avi']
            jpg_files = [f for f in files if f.suffix == '.jpg']
            
            if len(pkl_files) == 1 and len(avi_files) == 1 and len(jpg_files) == 3:
                total_samples += 1
    
    print(f"✅ 检测到 {total_samples} 个有效样本文件夹")
    
    # 优化功能说明
    print("\n🚀 优化功能特点:")
    print("1. 📱 隐藏已处理样本 - 只显示未处理的样本，界面更清爽")
    print("2. ⚡ 懒加载优化 - 快速启动，后台生成拼接图片")
    print("3. 🎯 多线程处理 - 使用4个线程并行生成图片")
    print("4. 📊 实时进度条 - 显示图片生成进度")
    print("5. 🖱️ 增强双击 - 多种播放器支持，详细日志")
    print("6. 🔄 动态更新 - 图片生成完成后自动更新界面")
    
    print("\n📋 测试指南:")
    print("启动后请测试以下功能：")
    print()
    print("⭐ 性能优化测试:")
    print("  - 观察启动速度（应该很快，不会卡在图片生成）")
    print("  - 查看进度条显示（页面顶部应该显示生成进度）")
    print("  - 等待所有图片生成完成")
    print()
    print("⭐ 界面功能测试:")
    print("  - 只应该显示未处理的样本")
    print("  - 已处理/已删除的样本应该被隐藏")
    print("  - 统计信息应该显示正确的数量")
    print()
    print("⭐ 双击播放测试:")
    print("  - 双击任意样本卡片")
    print("  - 观察控制台日志（按F12打开开发者工具）")
    print("  - 确认视频播放器是否打开")
    print("  - 如果失败，查看详细错误信息")
    print()
    print("⭐ 批量标注测试:")
    print("  - 选择多个样本")
    print("  - 使用标签按钮进行批量标注")
    print("  - 观察样本是否从界面中消失（因为已被处理）")
    
    # Linux系统特别说明
    if platform.system() == "Linux":
        print("\n🐧 Linux系统特别说明:")
        print("  - 新版本会尝试多种视频播放器：xdg-open, vlc, mpv, totem, mplayer")
        print("  - 建议安装VLC播放器：sudo apt install vlc")
        print("  - 或者安装MPV播放器：sudo apt install mpv")
        print("  - 查看终端输出获取详细的播放器尝试信息")
    
    # Windows系统说明
    elif platform.system() == "Windows":
        print("\n🪟 Windows系统说明:")
        print("  - 使用系统默认的视频播放器")
        print("  - 确保系统可以播放.avi格式视频")
    
    print(f"\n💡 样本规模适用性:")
    sample_ranges = [
        ("小规模", "20以下", "几秒内完成"),
        ("中规模", "50-200", "数十秒完成"),
        ("大规模", "200-1000", "几分钟完成"),
        ("超大规模", "1000-3000", "数分钟到十几分钟")
    ]
    
    for scale, count, time_est in sample_ranges:
        print(f"  {scale:6s}: {count:8s} 样本 → {time_est}")
    
    print(f"\n当前样本规模: {total_samples} 个样本")
    if total_samples < 20:
        print("  → 小规模，应该很快完成")
    elif total_samples < 200:
        print("  → 中等规模，优化效果明显")
    elif total_samples < 1000:
        print("  → 大规模，多线程优化很重要")
    else:
        print("  → 超大规模，懒加载+多线程发挥重要作用")
    
    start_test = input("\n是否启动优化版Web服务进行测试? (y/n): ").lower().strip()
    
    if start_test == 'y':
        print("\n🚀 正在启动优化版Web服务...")
        print("请关注以下几个方面：")
        print("1. 启动速度是否明显提升")
        print("2. 是否出现进度条显示")
        print("3. 双击播放是否正常工作")
        print("4. 标注后样本是否正确隐藏")
        print("\n按 Ctrl+C 停止服务")
        
        try:
            # 启动Web服务
            subprocess.run(['python', str(web_script)], check=True)
        except KeyboardInterrupt:
            print("\n✅ 测试完成!")
        except subprocess.CalledProcessError as e:
            print(f"❌ 启动失败: {e}")
    else:
        print("测试取消")

if __name__ == "__main__":
    test_optimizations()

# -*-coding:utf-8-*-
"""
created By @Moss 2025.03.28
图像标注工具 - Web版本

需求2功能：
1. 在本地网页中，将拼接后展示的部分可视化在网页中
2. 将原来1次只能拼接和查看1张图片，调整为在网页中拼接和查看多个样本文件夹
3. 在网页中可以选中多个拼接后的样本，然后按键1次，将这些样本移动到标签位置

兼容Windows 10系统
"""

import os
import shutil
import re
import json
import uuid
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_from_directory
from PIL import Image, ImageDraw
import threading
import webbrowser
import time
from concurrent.futures import ThreadPoolExecutor
import queue
import pickle
import torch
import numpy as np

# 使用条件创建Flask应用，避免PyCharm自动识别
app = Flask(__name__) if __name__ == '__main__' else None

# COCO 17关键点连接关系定义
COCO_SKELETON_CONNECTIONS = [
    (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
    (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身和手臂
    (5, 11), (6, 12), (11, 12),  # 躯干
    (11, 13), (13, 15), (12, 14), (14, 16)  # 腿部
]

# COCO 17关键点分组定义（用于朝向感知绘制）
# 0:鼻子, 1:左眼, 2:右眼, 3:左耳, 4:右耳, 5:左肩, 6:右肩, 7:左肘, 8:右肘
# 9:左腕, 10:右腕, 11:左髋, 12:右髋, 13:左膝, 14:右膝, 15:左脚踝, 16:右脚踝
LEFT_KEYPOINTS = [1, 3, 5, 7, 9, 11, 13, 15]  # 左侧关键点（朝右时绘制）
RIGHT_KEYPOINTS = [2, 4, 6, 8, 10, 12, 14, 16]  # 右侧关键点（朝左时绘制）
NEUTRAL_KEYPOINTS = [0]  # 中性关键点（始终绘制）


def determine_pose_orientation(skpt_frame: list) -> str:
    """
    根据左肩点和左脚踝点的x坐标判断骨骼朝向

    Args:
        skpt_frame: 骨骼关键点帧数据列表

    Returns:
        'right' 表示朝右（左肩x < 左脚踝x），'left' 表示朝左
    """
    if not skpt_frame:
        return 'right'  # 默认朝向

    orientation_votes = []

    for frame_points in skpt_frame:
        if len(frame_points) >= 17:  # 确保有足够的关键点
            try:
                left_shoulder = frame_points[5]  # 左肩点索引5
                left_ankle = frame_points[15]    # 左脚踝点索引15

                left_shoulder_x = left_shoulder[0].item() if hasattr(left_shoulder[0], 'item') else left_shoulder[0]
                left_ankle_x = left_ankle[0].item() if hasattr(left_ankle[0], 'item') else left_ankle[0]

                # 左肩x < 左脚踝x 认为是朝右，反之朝左
                if left_shoulder_x < left_ankle_x:
                    orientation_votes.append('right')
                else:
                    orientation_votes.append('left')
            except (IndexError, AttributeError, TypeError):
                continue

    if not orientation_votes:
        return 'right'  # 默认朝向

    # 多数投票决定最终朝向
    right_votes = orientation_votes.count('right')
    left_votes = orientation_votes.count('left')

    return 'right' if right_votes >= left_votes else 'left'


def get_frame_color(frame_index: int, total_frames: int) -> tuple:
    """
    获取帧的颜色，每4帧使用相同颜色，整体从深色渐变到浅色
    """
    color_group = frame_index // 4
    max_groups = (total_frames + 3) // 4  # 向上取整

    # 从深蓝到浅蓝的渐变
    if max_groups <= 1:
        intensity = 0.8
    else:
        intensity = 0.3 + 0.7 * (color_group / (max_groups - 1))

    # RGB颜色值
    r = int(intensity * 173)
    g = int(intensity * 216)
    b = int(128 + intensity * 102)

    return (r, g, b)


def normalize_skeleton_coords(skpt_frame: list, target_width: int, target_height: int) -> list:
    """
    将骨骼坐标归一化并映射到目标画布尺寸
    """
    if not skpt_frame:
        return []

    # 合并所有帧的关键点
    all_points = torch.cat(skpt_frame, dim=0)

    # 计算边界框
    min_x, min_y = torch.min(all_points, dim=0)[0]
    max_x, max_y = torch.max(all_points, dim=0)[0]

    # 添加边距
    margin_x = (max_x - min_x) * 0.1
    margin_y = (max_y - min_y) * 0.1

    min_x -= margin_x
    max_x += margin_x
    min_y -= margin_y
    max_y += margin_y

    # 计算缩放比例，保持宽高比
    scale_x = target_width / (max_x - min_x)
    scale_y = target_height / (max_y - min_y)
    scale = min(scale_x, scale_y)

    # 计算居中偏移
    scaled_width = (max_x - min_x) * scale
    scaled_height = (max_y - min_y) * scale
    offset_x = (target_width - scaled_width) / 2
    offset_y = (target_height - scaled_height) / 2

    # 归一化所有帧的坐标
    normalized_frames = []
    for frame_points in skpt_frame:
        normalized_points = []
        for point in frame_points:
            x, y = point[0].item(), point[1].item()
            new_x = (x - min_x) * scale + offset_x
            new_y = (y - min_y) * scale + offset_y
            normalized_points.append((new_x, new_y))
        normalized_frames.append(normalized_points)

    return normalized_frames


def filter_keypoints_by_orientation(keypoints: list, orientation: str) -> list:
    """
    根据朝向过滤关键点，返回需要绘制的关键点索引

    Args:
        keypoints: 关键点列表
        orientation: 朝向 ('right' 或 'left')

    Returns:
        过滤后的关键点列表，包含(索引, 坐标)元组
    """
    if orientation == 'right':
        # 朝右时绘制左侧关键点
        target_indices = LEFT_KEYPOINTS + NEUTRAL_KEYPOINTS
    else:
        # 朝左时绘制右侧关键点
        target_indices = RIGHT_KEYPOINTS + NEUTRAL_KEYPOINTS

    filtered_keypoints = []
    for i, point in enumerate(keypoints):
        if i in target_indices:
            filtered_keypoints.append((i, point))

    return filtered_keypoints


def filter_connections_by_orientation(connections: list, orientation: str) -> list:
    """
    根据朝向过滤连接线

    Args:
        connections: 连接线列表
        orientation: 朝向 ('right' 或 'left')

    Returns:
        过滤后的连接线列表
    """
    if orientation == 'right':
        # 朝右时绘制左侧关键点相关的连接线
        target_indices = set(LEFT_KEYPOINTS + NEUTRAL_KEYPOINTS)
    else:
        # 朝左时绘制右侧关键点相关的连接线
        target_indices = set(RIGHT_KEYPOINTS + NEUTRAL_KEYPOINTS)

    filtered_connections = []
    for start_idx, end_idx in connections:
        # 只保留两个端点都在目标索引集合中的连接线
        if start_idx in target_indices and end_idx in target_indices:
            filtered_connections.append((start_idx, end_idx))

    return filtered_connections


def draw_skeleton_frame(draw: ImageDraw.Draw, keypoints: list, color: tuple, orientation: str = 'right', point_radius: int = 3, line_width: int = 2):
    """
    在图像上绘制单帧的骨骼关键点和连接线（支持朝向感知）

    Args:
        draw: PIL绘制对象
        keypoints: 关键点坐标列表
        color: 绘制颜色
        orientation: 朝向 ('right' 或 'left')
        point_radius: 关键点半径
        line_width: 连接线宽度
    """
    # 根据朝向过滤连接线
    filtered_connections = filter_connections_by_orientation(COCO_SKELETON_CONNECTIONS, orientation)

    # 绘制过滤后的连接线
    for start_idx, end_idx in filtered_connections:
        if start_idx < len(keypoints) and end_idx < len(keypoints):
            start_point = keypoints[start_idx]
            end_point = keypoints[end_idx]
            draw.line([start_point, end_point], fill=color, width=line_width)

    # 根据朝向过滤并绘制关键点
    filtered_keypoints = filter_keypoints_by_orientation(keypoints, orientation)
    for idx, point in filtered_keypoints:
        x, y = point
        draw.ellipse([x-point_radius, y-point_radius, x+point_radius, y+point_radius],
                    fill=color, outline=color)


def generate_skeleton_image(pkl_file_path: str, target_width: int, target_height: int) -> str:
    """
    从pkl文件生成骨骼图片

    Args:
        pkl_file_path: pkl文件路径
        target_width: 目标图片宽度
        target_height: 目标图片高度

    Returns:
        生成的骨骼图片路径，失败时返回None
    """
    try:
        # 加载pkl文件
        with open(pkl_file_path, 'rb') as f:
            data = pickle.load(f)

        if 'pred_skpts' not in data:
            print(f"警告: pkl文件中没有pred_skpts数据: {pkl_file_path}")
            return None

        pred_skpts = data['pred_skpts']
        if not pred_skpts:
            print(f"警告: pred_skpts数据为空: {pkl_file_path}")
            return None

        # 提取骨骼关键点数据
        skpt_frame = [skpt_ts[0][6:].view(17,3)[:,:2] for skpt_ts in pred_skpts if skpt_ts is not None]

        if not skpt_frame:
            print(f"警告: 没有有效的骨骼帧数据: {pkl_file_path}")
            return None

        # 判断骨骼朝向
        orientation = determine_pose_orientation(skpt_frame)
        print(f"检测到骨骼朝向: {'朝右' if orientation == 'right' else '朝左'}")

        # 归一化坐标
        normalized_frames = normalize_skeleton_coords(skpt_frame, target_width, target_height)

        # 创建黑色背景图像
        skeleton_image = Image.new('RGB', (target_width, target_height), 'black')
        draw = ImageDraw.Draw(skeleton_image)

        # 绘制所有帧的骨骼（支持朝向感知）
        total_frames = len(normalized_frames)
        for frame_idx, keypoints in enumerate(normalized_frames):
            color = get_frame_color(frame_idx, total_frames)
            draw_skeleton_frame(draw, keypoints, color, orientation)

        return skeleton_image

    except Exception as e:
        print(f"生成骨骼图片失败: {e}")
        return None


# 全局配置
CONFIG = {
    'root_folder_path': '',
    'label_pth': '',
    'lab_log': '',
    'static_images_dir': '',
    'processed_samples': {},  # 存储已处理的样本信息
    'valid_keys': {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b', 'd'},
    'move_lab': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b'],
    'processing_queue': queue.Queue(),
    'processing_stats': {'total': 0, 'completed': 0, 'in_progress': False},
    'last_operation': None  # 存储最近一次的标注操作，用于撤销
}


def extract_number_from_filename(filename):
    """从文件名中提取末尾的数字"""
    match = re.search(r'-(\d+)\.jpg$', filename)
    return int(match.group(1)) if match else 0


def is_sample_folder(folder_path):
    """检查是否为样本文件夹（包含1个pkl，1个avi，3个jpg）"""
    folder_path = Path(folder_path)
    if not folder_path.is_dir():
        return False

    files = list(folder_path.iterdir())
    pkl_count = len([f for f in files if f.suffix == '.pkl'])
    avi_count = len([f for f in files if f.suffix == '.avi'])
    jpg_count = len([f for f in files if f.suffix == '.jpg'])

    return pkl_count == 1 and avi_count == 1 and jpg_count == 3


def concatenate_images_vertically(jpg_files, output_path, skeleton_image=None):
    """将jpg文件按照文件名末尾数字从小到大纵向拼接，可选择在底部添加骨骼图片"""
    # 按文件名末尾数字排序
    sorted_files = sorted(jpg_files, key=lambda x: extract_number_from_filename(x.name))

    # 打开所有图片
    images = []
    for jpg_file in sorted_files:
        try:
            img = Image.open(jpg_file)
            images.append(img)
        except Exception as e:
            print(f"无法打开图片 {jpg_file}: {e}")
            return None

    if not images:
        return None

    # 如果有骨骼图片，添加到列表中
    if skeleton_image:
        images.append(skeleton_image)

    # 计算拼接后图片的尺寸
    max_width = max(img.width for img in images)
    total_height = sum(img.height for img in images)

    # 创建新的空白图片
    concatenated = Image.new('RGB', (max_width, total_height), 'white')

    # 纵向拼接图片
    current_height = 0
    for img in images:
        # 如果图片宽度小于最大宽度，居中放置
        x_offset = (max_width - img.width) // 2
        concatenated.paste(img, (x_offset, current_height))
        current_height += img.height

    # 保存拼接后的图片
    try:
        concatenated.save(output_path)
        return output_path
    except Exception as e:
        print(f"无法保存拼接图片: {e}")
        return None


def write_lab2txt(lab_log, sample_folder_name, lab):
    """记录标签信息到日志文件"""
    try:
        with open(lab_log, 'a+', encoding='utf-8') as f:
            f.write(f"{sample_folder_name} {lab}\n")
        return True
    except Exception as e:
        print(f"写入日志失败: {e}")
        return False


def calculate_label_stats(samples):
    """计算各个标签的统计数量"""
    stats = {
        'total': len(samples),
        'unprocessed': 0,
        'processed': 0,
        'deleted': 0,
        'labels': {}  # 各个标签的数量
    }

    # 初始化所有标签计数
    for label in CONFIG['move_lab']:
        stats['labels'][label] = 0

    # 统计各种状态和标签
    for sample in samples:
        status = sample.get('status', 'unprocessed')

        if status == 'unprocessed':
            stats['unprocessed'] += 1
        elif status == 'processed':
            stats['processed'] += 1
            # 统计具体标签
            label = sample.get('label')
            if label and label in stats['labels']:
                stats['labels'][label] += 1
        elif status == 'deleted':
            stats['deleted'] += 1

    return stats


def initialize_config(root_path):
    """初始化配置"""
    global CONFIG

    CONFIG['root_folder_path'] = str(Path(root_path))
    CONFIG['label_pth'] = Path(root_path).parent / (Path(root_path).stem + '_labels')
    CONFIG['label_pth'].mkdir(exist_ok=True)

    CONFIG['lab_log'] = CONFIG['label_pth'] / 'sample_labels_web.txt'
    CONFIG['lab_log'].touch(exist_ok=True)

    # 创建静态图片目录
    CONFIG['static_images_dir'] = Path(__file__).parent / 'static' / 'processed_images'
    CONFIG['static_images_dir'].mkdir(parents=True, exist_ok=True)


def scan_all_samples():
    """快速扫描所有样本文件夹，不生成拼接图片"""
    global CONFIG

    root_path = Path(CONFIG['root_folder_path'])
    processed_samples = {}

    sample_id = 0
    for item in root_path.rglob('*'):
        if item.is_dir() and is_sample_folder(item):
            sample_id += 1

            # 获取jpg文件和pkl文件
            jpg_files = list(item.glob('*.jpg'))
            pkl_files = list(item.glob('*.pkl'))
            if len(jpg_files) != 3:
                continue

            # 存储样本信息（暂时不生成拼接图片）
            processed_samples[sample_id] = {
                'id': sample_id,
                'name': item.name,
                'folder_path': str(item),
                'concatenated_image': None,  # 待生成
                'jpg_files': [str(f) for f in jpg_files],
                'pkl_files': [str(f) for f in pkl_files],
                'status': 'unprocessed',
                'image_generated': False
            }

            print(f"扫描到样本: {item.name}")

    CONFIG['processed_samples'] = processed_samples
    CONFIG['processing_stats'] = {'total': len(processed_samples), 'completed': 0, 'in_progress': False}

    return len(processed_samples)


def generate_sample_image(sample_id):
    """为指定样本生成拼接图片"""
    global CONFIG

    if sample_id not in CONFIG['processed_samples']:
        return False

    sample = CONFIG['processed_samples'][sample_id]
    if sample['image_generated']:
        return True  # 已经生成过

    folder_path = Path(sample['folder_path'])
    jpg_files = [Path(f) for f in sample['jpg_files']]

    # 生成骨骼图片
    skeleton_image = None
    pkl_files = [Path(f) for f in sample.get('pkl_files', [])]
    if pkl_files:
        pkl_file = pkl_files[0]  # 取第一个pkl文件
        # 获取第一张jpg图片的尺寸作为骨骼图片的目标尺寸
        try:
            first_img = Image.open(jpg_files[0])
            target_width, target_height = first_img.width, first_img.height
            skeleton_image = generate_skeleton_image(str(pkl_file), target_width, target_height)
            if skeleton_image:
                print(f"骨骼图片生成成功: {sample['name']}")
            else:
                print(f"骨骼图片生成失败，将只拼接原有3张图片: {sample['name']}")
        except Exception as e:
            print(f"生成骨骼图片时出错: {e}")

    # 生成拼接图片（包含骨骼图片）
    concatenated_filename = f"sample_{sample_id:04d}_{sample['name']}.jpg"
    output_path = CONFIG['static_images_dir'] / concatenated_filename

    result = concatenate_images_vertically(jpg_files, output_path, skeleton_image)

    if result:
        # 更新样本信息
        CONFIG['processed_samples'][sample_id]['concatenated_image'] = f"processed_images/{concatenated_filename}"
        CONFIG['processed_samples'][sample_id]['image_generated'] = True
        CONFIG['processing_stats']['completed'] += 1

        print(f"生成图片完成: {sample['name']}")
        return True
    else:
        print(f"生成图片失败: {sample['name']}")
        return False


def start_background_image_generation():
    """启动后台图片生成任务"""
    global CONFIG

    CONFIG['processing_stats']['in_progress'] = True

    def generate_images():
        # 清理旧的处理图片
        for old_file in CONFIG['static_images_dir'].glob('*.jpg'):
            try:
                old_file.unlink()
            except:
                pass

        # 使用线程池并行生成图片
        with ThreadPoolExecutor(max_workers=4) as executor:
            sample_ids = list(CONFIG['processed_samples'].keys())
            executor.map(generate_sample_image, sample_ids)

        CONFIG['processing_stats']['in_progress'] = False
        print("所有样本图片生成完成!")

    # 在后台线程中执行
    thread = threading.Thread(target=generate_images, daemon=True)
    thread.start()


# 保持向后兼容
def process_all_samples():
    """兼容性函数：快速扫描 + 后台生成"""
    count = scan_all_samples()
    if count > 0:
        start_background_image_generation()
    return count


def create_web_app():
    """创建并配置Web应用"""
    # 动态导入Flask相关模块
    from flask import Flask, render_template, request, jsonify, send_from_directory

    app = Flask(__name__)

    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')


    @app.route('/api/samples')
    def get_samples():
        """获取所有样本信息"""
        samples = list(CONFIG['processed_samples'].values())
        stats = calculate_label_stats(samples)

        return jsonify({
            'success': True,
            'samples': samples,
            'label_stats': stats
        })


    @app.route('/api/label', methods=['POST'])
    def label_samples():
        """批量标注样本"""
        try:
            data = request.get_json()
            sample_ids = data.get('sample_ids', [])
            label = data.get('label', '')

            if not sample_ids or label not in CONFIG['valid_keys']:
                return jsonify({'success': False, 'message': '无效的参数'})

            results = []
            successful_operations = []  # 记录成功的操作，用于撤销

            for sample_id in sample_ids:
                sample_id = int(sample_id)
                if sample_id not in CONFIG['processed_samples']:
                    continue

                sample = CONFIG['processed_samples'][sample_id]
                folder_path = Path(sample['folder_path'])

                if not folder_path.exists():
                    results.append({
                        'sample_id': sample_id,
                        'success': False,
                        'message': '样本文件夹不存在'
                    })
                    continue

                if label in CONFIG['move_lab']:
                    # 移动样本文件夹到标签目录
                    dest_folder = CONFIG['label_pth'] / label
                    dest_folder.mkdir(exist_ok=True)

                    dest_sample_folder = dest_folder / folder_path.name

                    try:
                        # 记录原始位置，用于撤销
                        original_path = str(folder_path)

                        shutil.move(str(folder_path), str(dest_sample_folder))
                        write_lab2txt(CONFIG['lab_log'], folder_path.name, label)

                        # 更新样本状态
                        CONFIG['processed_samples'][sample_id]['status'] = 'processed'
                        CONFIG['processed_samples'][sample_id]['label'] = label

                        # 记录成功的操作（只记录移动操作，不记录删除操作）
                        successful_operations.append({
                            'sample_id': sample_id,
                            'sample_name': folder_path.name,
                            'label': label,
                            'original_path': original_path,
                            'target_path': str(dest_sample_folder),
                            'operation_type': 'move'
                        })

                        results.append({
                            'sample_id': sample_id,
                            'success': True,
                            'message': f'已移动到标签 {label}'
                        })

                    except Exception as e:
                        results.append({
                            'sample_id': sample_id,
                            'success': False,
                            'message': f'移动失败: {str(e)}'
                        })

                elif label == 'd':
                    # 删除样本文件夹
                    try:
                        shutil.rmtree(folder_path)

                        # 更新样本状态
                        CONFIG['processed_samples'][sample_id]['status'] = 'deleted'

                        results.append({
                            'sample_id': sample_id,
                            'success': True,
                            'message': '已删除'
                        })

                    except Exception as e:
                        results.append({
                            'sample_id': sample_id,
                            'success': False,
                            'message': f'删除失败: {str(e)}'
                        })
                else:
                    results.append({
                        'sample_id': sample_id,
                        'success': False,
                        'message': '无效的标签'
                    })

            # 保存最近的标注操作历史（只保存移动操作，不保存删除操作）
            if successful_operations:
                import datetime
                CONFIG['last_operation'] = {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'operations': successful_operations,
                    'label': label  # 用于显示撤销信息
                }
                print(f"[DEBUG] 保存操作历史: {len(successful_operations)} 个样本标注为 {label}")

            return jsonify({
                'success': True,
                'results': results
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'处理失败: {str(e)}'
            })


    @app.route('/api/refresh')
    def refresh_samples():
        """重新扫描和处理样本"""
        try:
            count = process_all_samples()
            samples = list(CONFIG['processed_samples'].values())
            label_stats = calculate_label_stats(samples)

            return jsonify({
                'success': True,
                'message': f'重新扫描完成，找到 {count} 个样本',
                'samples': samples,
                'label_stats': label_stats
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'扫描失败: {str(e)}'
            })


    @app.route('/api/processing_status')
    def processing_status():
        """获取图片生成进度"""
        samples = list(CONFIG['processed_samples'].values())
        label_stats = calculate_label_stats(samples)

        return jsonify({
        'success': True,
        'stats': CONFIG['processing_stats'],
        'samples': samples,
        'label_stats': label_stats
        })


    @app.route('/api/generate_image/<int:sample_id>')
    def generate_single_image(sample_id):
        """为指定样本生成拼接图片"""
        try:
            success = generate_sample_image(sample_id)
            if success:
                sample = CONFIG['processed_samples'][sample_id]
                return jsonify({
                    'success': True,
                    'message': '图片生成成功',
                    'sample': sample
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '图片生成失败'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'生成失败: {str(e)}'
            })


    @app.route('/api/undo', methods=['POST'])
    def undo_last_operation():
        """撤销最近一次的标注操作"""
        try:
            if not CONFIG['last_operation']:
                return jsonify({
                    'success': False,
                    'message': '没有可以撤销的操作'
                })

            last_op = CONFIG['last_operation']
            operations = last_op['operations']

            print(f"[DEBUG] 开始撤销操作: {len(operations)} 个样本")

            undone_sample_ids = []
            errors = []

            for op in operations:
                try:
                    sample_id = op['sample_id']
                    target_path = Path(op['target_path'])
                    original_path = Path(op['original_path'])

                    print(f"[DEBUG] 撤销样本 {sample_id}: {target_path} -> {original_path}")

                    # 检查目标文件是否存在
                    if not target_path.exists():
                        print(f"[WARNING] 目标路径不存在，跳过: {target_path}")
                        continue

                    # 确保原始目录存在
                    original_path.parent.mkdir(parents=True, exist_ok=True)

                    # 移动文件夹回原位置
                    shutil.move(str(target_path), str(original_path))

                    # 更新样本状态
                    if sample_id in CONFIG['processed_samples']:
                        CONFIG['processed_samples'][sample_id]['status'] = 'unprocessed'
                        if 'label' in CONFIG['processed_samples'][sample_id]:
                            del CONFIG['processed_samples'][sample_id]['label']

                    # 从日志文件中移除记录
                    try:
                        remove_from_log(CONFIG['lab_log'], op['sample_name'])
                    except Exception as log_error:
                        print(f"[WARNING] 移除日志记录失败: {log_error}")

                    undone_sample_ids.append(sample_id)
                    print(f"[DEBUG] 成功撤销样本 {sample_id}")

                except Exception as e:
                    error_msg = f"撤销样本 {op['sample_id']} 失败: {str(e)}"
                    print(f"[ERROR] {error_msg}")
                    errors.append(error_msg)

            if undone_sample_ids:
                # 清除最近操作记录
                CONFIG['last_operation'] = None

                message = f"成功撤销 {len(undone_sample_ids)} 个样本的标注"
                if errors:
                    message += f"，{len(errors)} 个失败"

                return jsonify({
                    'success': True,
                    'message': message,
                    'undone_sample_ids': undone_sample_ids,
                    'errors': errors,
                    'samples': list(CONFIG['processed_samples'].values())
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '没有成功撤销任何操作',
                    'errors': errors
                })

        except Exception as e:
            error_msg = f'撤销失败: {str(e)}'
            print(f"[ERROR] {error_msg}")
            return jsonify({
                'success': False,
                'message': error_msg
            })


def remove_from_log(log_file, sample_name):
    """从日志文件中移除指定样本的记录"""
    try:
        if not Path(log_file).exists():
            return

        # 读取所有行
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 过滤掉包含指定样本名称的行
        filtered_lines = [line for line in lines if not line.strip().startswith(sample_name)]

        # 写回文件
        with open(log_file, 'w', encoding='utf-8') as f:
            f.writelines(filtered_lines)

        print(f"[DEBUG] 从日志中移除样本记录: {sample_name}")

    except Exception as e:
        print(f"[ERROR] 移除日志记录失败: {e}")


    @app.route('/api/can_undo')
    def can_undo():
        """检查是否可以撤销"""
        has_operation = CONFIG['last_operation'] is not None
        if has_operation:
            last_op = CONFIG['last_operation']
            return jsonify({
                'success': True,
                'can_undo': True,
                'operation_info': {
                    'timestamp': last_op['timestamp'],
                    'count': len(last_op['operations']),
                    'label': last_op['label']
                }
            })
        else:
            return jsonify({
                'success': True,
                'can_undo': False
            })


    @app.route('/api/play_video', methods=['POST'])
    def play_video():
        """播放样本视频"""
        try:
        data = request.get_json()
        sample_id = data.get('sample_id')

        print(f"[DEBUG] 收到播放视频请求，样本ID: {sample_id}")

        if not sample_id or sample_id not in CONFIG['processed_samples']:
            print(f"[ERROR] 样本不存在: {sample_id}")
            return jsonify({'success': False, 'message': '样本不存在'})

        sample = CONFIG['processed_samples'][sample_id]
        folder_path = Path(sample['folder_path'])

        print(f"[DEBUG] 样本文件夹路径: {folder_path}")

        if not folder_path.exists():
            print(f"[ERROR] 样本文件夹不存在: {folder_path}")
            return jsonify({'success': False, 'message': '样本文件夹不存在'})

        # 查找.avi文件
        avi_files = list(folder_path.glob('*.avi'))
        print(f"[DEBUG] 找到的视频文件: {avi_files}")

        if not avi_files:
            return jsonify({'success': False, 'message': '找不到视频文件'})

        avi_file = avi_files[0]  # 取第一个.avi文件

        print(f"[DEBUG] 准备播放视频: {avi_file}")
        print(f"[DEBUG] 文件是否存在: {avi_file.exists()}")
        print(f"[DEBUG] 文件大小: {avi_file.stat().st_size if avi_file.exists() else 'N/A'}")

        # 使用系统默认程序打开视频
        import platform
        import subprocess

        system_type = platform.system()
        print(f"[DEBUG] 操作系统类型: {system_type}")

        try:
            if system_type == "Windows":
                print("[DEBUG] 使用Windows方式打开视频")
                os.startfile(str(avi_file))

            elif system_type == "Darwin":  # macOS
                print("[DEBUG] 使用macOS方式打开视频")
                result = subprocess.run(['open', str(avi_file)],
                                      capture_output=True, text=True, timeout=10)
                print(f"[DEBUG] macOS命令执行结果: {result}")

            else:  # Linux
                print("[DEBUG] 使用Linux方式打开视频")

                # 尝试多种Linux视频播放器
                players = [
                    ['xdg-open', str(avi_file)],
                    ['vlc', str(avi_file)],
                    ['mpv', str(avi_file)],
                    ['totem', str(avi_file)],
                    ['mplayer', str(avi_file)]
                ]

                success = False
                last_error = None

                for player_cmd in players:
                    try:
                        print(f"[DEBUG] 尝试使用: {player_cmd[0]}")
                        result = subprocess.run(player_cmd,
                                              capture_output=True,
                                              text=True,
                                              timeout=5,
                                              check=False)
                        print(f"[DEBUG] {player_cmd[0]} 执行结果 - 返回码: {result.returncode}")
                        if result.stderr:
                            print(f"[DEBUG] {player_cmd[0]} 错误输出: {result.stderr}")
                        if result.stdout:
                            print(f"[DEBUG] {player_cmd[0]} 标准输出: {result.stdout}")

                        if result.returncode == 0:
                            success = True
                            print(f"[DEBUG] 成功使用 {player_cmd[0]} 打开视频")
                            break

                    except subprocess.TimeoutExpired:
                        print(f"[DEBUG] {player_cmd[0]} 执行超时")
                        continue
                    except FileNotFoundError:
                        print(f"[DEBUG] {player_cmd[0]} 未找到")
                        continue
                    except Exception as e:
                        print(f"[DEBUG] {player_cmd[0]} 执行失败: {e}")
                        last_error = str(e)
                        continue

                if not success:
                    error_msg = f"尝试了多种播放器都无法打开视频文件。最后一个错误: {last_error}"
                    print(f"[ERROR] {error_msg}")
                    return jsonify({
                        'success': False,
                        'message': error_msg
                    })

            return jsonify({
                'success': True,
                'message': f'正在播放视频: {avi_file.name}'
            })

        except Exception as e:
            error_msg = f'无法打开视频: {str(e)}'
            print(f"[ERROR] {error_msg}")
            return jsonify({
                'success': False,
                'message': error_msg
            })

        except Exception as e:
        error_msg = f'播放失败: {str(e)}'
        print(f"[ERROR] {error_msg}")
        return jsonify({
            'success': False,
            'message': error_msg
        })


    @app.route('/api/open_folder', methods=['POST'])
    def open_folder():
        """在系统文件管理器中打开样本所在文件夹"""
        try:
        data = request.get_json()
        sample_id = data.get('sample_id')

        if not sample_id or sample_id not in CONFIG['processed_samples']:
            return jsonify({'success': False, 'message': '样本不存在'})

        sample = CONFIG['processed_samples'][sample_id]
        folder_path = Path(sample['folder_path'])

        if not folder_path.exists():
            return jsonify({'success': False, 'message': '样本文件夹不存在'})

        import platform
        import subprocess

        system_type = platform.system()
        try:
            if system_type == "Windows":
                os.startfile(str(folder_path))
            elif system_type == "Darwin":  # macOS
                subprocess.Popen(['open', str(folder_path)])
            else:  # Linux
                subprocess.Popen(['xdg-open', str(folder_path)])

            return jsonify({'success': True, 'message': f'已打开文件夹: {folder_path.name}'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'无法打开文件夹: {str(e)}'})

        except Exception as e:
        return jsonify({'success': False, 'message': f'处理失败: {str(e)}'})


    @app.route('/static/processed_images/<filename>')
    def processed_image(filename):
        """提供处理后的图片"""
        return send_from_directory(CONFIG['static_images_dir'], filename)

        return app


def create_html_template():
    """创建HTML模板文件"""
    template_dir = Path(__file__).parent / 'templates'
    template_dir.mkdir(exist_ok=True)

    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SitUp图组标注工具 - Moss版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .label-buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .samples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .sample-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .sample-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .sample-card.selected {
            border: 3px solid #007bff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .sample-card.processed {
            opacity: 0.6;
            border: 2px solid #28a745;
        }
        .sample-card.deleted {
            opacity: 0.4;
            border: 2px solid #dc3545;
        }
        .sample-image {
            width: 100%;
            height: 400px;
            object-fit: contain;
            background-color: #f8f9fa;
            transition: transform 0.2s ease;
        }
        .sample-card:hover .sample-image {
            transform: scale(1.1);
        }
        .sample-info {
            padding: 15px;
        }
        .sample-name {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 12px;
            word-break: break-all;
        }
        .sample-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        .status-unprocessed {
            background-color: #e9ecef;
            color: #495057;
        }
        .status-processed {
            background-color: #d4edda;
            color: #155724;
        }
        .status-deleted {
            background-color: #f8d7da;
            color: #721c24;
        }
        .selection-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        /* 固定右上角的消息容器 */
        #message-container {
            position: fixed;
            top: 16px;
            right: 16px;
            z-index: 2000;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
            pointer-events: none; /* 允许下方元素交互 */
            max-width: min(420px, 90vw);
        }
        #message-container .message {
            pointer-events: auto; /* 消息本身可选中/复制 */
            margin: 0; /* 由容器gap控制间距 */
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        @media (max-width: 480px) {
            #message-container {
                top: 8px;
                right: 8px;
                max-width: 92vw;
            }
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .message {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .sample-image {
            cursor: pointer;
        }
        .sample-card {
            user-select: none; /* 防止选择文本 */
        }
        .sample-card:active {
            transform: scale(0.98);
        }

        /* 内联标签按钮样式 */
        .sample-label-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 3px;
            margin-top: 8px;
            padding: 6px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 6px;
            border-top: 1px solid #e9ecef;
            align-items: center;
        }

        .label-btn-small {
            padding: 2px 6px;
            font-size: 10px;
            font-weight: bold;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 18px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .label-btn-small.primary {
            background: #007bff;
            color: white;
        }

        .label-btn-small.primary:hover {
            background: #0056b3;
            transform: scale(1.1);
        }

        .label-btn-small.warning {
            background: #ffc107;
            color: #212529;
        }

        .label-btn-small.warning:hover {
            background: #e0a800;
            transform: scale(1.1);
        }

        .label-btn-small.danger {
            background: #dc3545;
            color: white;
        }

        .label-btn-small.danger:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        /* 删除按钮居右 */
        .label-btn-small.delete-btn {
            margin-left: auto;
        }

        /* 统计信息美化 */
        #sample-stats {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 15px;
            font-size: 14px;
            line-height: 1.6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .stats-label {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            margin-right: 4px;
            font-weight: bold;
        }

        .stats-count {
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>SitUp图组标注工具 - Moss版</h1>
        <div id="sample-stats" style="margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 14px; color: #6c757d;">
            正在加载样本统计信息...
        </div>
        <div id="progress-container" style="margin-bottom: 10px; display: none;">
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                    <span id="progress-text">正在生成拼接图片...</span>
                    <span id="progress-percentage">0%</span>
                </div>
                <div style="background: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden;">
                    <div id="progress-bar" style="background: #007bff; height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
            </div>
        </div>
        <div class="controls">
            <button class="btn btn-primary" onclick="refreshSamples()">刷新样本</button>
            <button class="btn btn-primary" onclick="selectAll()">全选</button>
            <button class="btn btn-primary" onclick="clearSelection()">清除选择</button>
            <div class="selection-info">
                已选择: <span id="selected-count">0</span> 个样本
            </div>
        </div>
        <div class="controls" style="margin-top: 10px;">
            <span>标签操作:</span>
            <div class="label-buttons">
                <button class="btn btn-success" onclick="labelSelected('0')">标签 0</button>
                <button class="btn btn-success" onclick="labelSelected('1')">标签 1</button>
                <button class="btn btn-success" onclick="labelSelected('2')">标签 2</button>
                <button class="btn btn-success" onclick="labelSelected('3')">标签 3</button>
                <button class="btn btn-success" onclick="labelSelected('4')">标签 4</button>
                <button class="btn btn-success" onclick="labelSelected('5')">标签 5</button>
                <button class="btn btn-success" onclick="labelSelected('6')">标签 6</button>
                <button class="btn btn-success" onclick="labelSelected('7')">标签 7</button>
                <button class="btn btn-success" onclick="labelSelected('8')">标签 8</button>
                <button class="btn btn-success" onclick="labelSelected('9')">标签 9</button>
                <button class="btn btn-warning" onclick="labelSelected('b')">标签 b</button>
                <button class="btn btn-danger" onclick="labelSelected('d')">删除</button>
            </div>
        </div>
        <div class="controls" style="margin-top: 10px;">
            <div style="background: #e9ecef; padding: 10px; border-radius: 4px; font-size: 14px;">
                <strong>操作说明:</strong> 单击样本卡片选择/取消选择，双击播放视频，<strong>点击样本下方标签按钮直接标注</strong>，支持键盘快捷键 0-9、b、d，<strong>Ctrl+Z 撤销</strong>
            </div>
        </div>
        <div id="undo-status" style="margin-top: 10px; display: none;">
            <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 10px; font-size: 14px; color: #0c5460;">
                <span id="undo-info">可以撤销最近的标注操作</span>
                <button class="btn btn-sm" onclick="undoLastOperation()" style="margin-left: 10px; padding: 4px 8px; font-size: 12px; background: #17a2b8; color: white;">
                    撤销 (Ctrl+Z)
                </button>
            </div>
        </div>
    </div>

    <div id="message-container"></div>
    <div id="samples-container" class="loading">
        正在加载样本...
    </div>





    <script>
        let samples = [];
        let selectedSamples = new Set();
        let progressInterval = null;

        async function loadSamples(preserveSelection = false) {
            try {
                const response = await fetch('/api/samples');
                const data = await response.json();

                if (data.success) {
                    samples = data.samples;

                    // 更新统计信息
                    if (data.label_stats) {
                        updateStatistics(data.label_stats);
                    }

                    // 如果不保留选中状态，则清除选中
                    if (!preserveSelection) {
                        // 正常加载，可能需要监控进度
                        renderSamples();
                        startProgressMonitoring();
                    } else {
                        // 保留选中状态，只渲染样本
                        renderSamples();
                    }

                    checkUndoStatus(); // 检查是否有可撤销的操作
                } else {
                    showMessage('加载样本失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        function startProgressMonitoring() {
            // 开始监控处理进度
            progressInterval = setInterval(async () => {
                try {
                    const response = await fetch('/api/processing_status');
                    const data = await response.json();

                    if (data.success) {
                        updateProgress(data.stats);

                        // 更新统计信息
                        if (data.label_stats) {
                            updateStatistics(data.label_stats);
                        }

                        // 更新样本数据（有些图片可能已经生成完成）
                        if (JSON.stringify(data.samples) !== JSON.stringify(samples)) {
                            samples = data.samples;
                            renderSamples();
                        }

                        // 如果处理完成，停止监控
                        if (!data.stats.in_progress && data.stats.completed === data.stats.total) {
                            clearInterval(progressInterval);
                            hideProgress();
                        }
                    }
                } catch (error) {
                    console.error('获取进度失败:', error);
                }
            }, 1000); // 每秒更新一次
        }

        function updateProgress(stats) {
            if (stats.total === 0) return;

            const percentage = Math.round((stats.completed / stats.total) * 100);
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const progressPercentage = document.getElementById('progress-percentage');

            if (stats.in_progress || stats.completed < stats.total) {
                progressContainer.style.display = 'block';
                progressBar.style.width = percentage + '%';
                progressText.textContent = `正在生成拼接图片 (${stats.completed}/${stats.total})`;
                progressPercentage.textContent = percentage + '%';
            }
        }

        function hideProgress() {
            const progressContainer = document.getElementById('progress-container');
            progressContainer.style.display = 'none';
            showMessage('所有图片生成完成！', 'success');
        }

        function renderSamples() {
            const container = document.getElementById('samples-container');

            if (samples.length === 0) {
                container.innerHTML = '<div class="loading">没有找到样本文件夹</div>';
                return;
            }

            // 过滤掉已处理和已删除的样本，只显示未处理的
            const unprocessedSamples = samples.filter(sample => sample.status === 'unprocessed');

            if (unprocessedSamples.length === 0) {
                container.innerHTML = '<div class="loading">所有样本都已处理完成！</div>';
                return;
            }

            container.innerHTML = '<div class="samples-grid">' + unprocessedSamples.map(sample => `
                <div class="sample-card ${sample.status} ${selectedSamples.has(sample.id) ? 'selected' : ''}"
                     data-sample-id="${sample.id}"
                     onclick="toggleSelect(${sample.id})"
                     ondblclick="playVideo(${sample.id})"
                     title="单击选择，双击播放视频">
                    ${sample.image_generated && sample.concatenated_image ?
                        `<img src="/static/${sample.concatenated_image}" alt="${sample.name}" class="sample-image" />` :
                        `<div class="sample-image" style="display: flex; align-items: center; justify-content: center; background: #f8f9fa; color: #6c757d;">
                            <div style="text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 10px;">📷</div>
                                <div>正在生成图片...</div>
                            </div>
                         </div>`
                    }
                    <div class="sample-info">
                        <div class="sample-name">${sample.name}</div>
                        ${sample.status !== 'unprocessed' ? `
                            <span class="sample-status status-${sample.status}">
                                ${sample.status === 'processed' ? '已处理 (' + (sample.label || '') + ')' : '已删除'}
                            </span>
                        ` : ''}
                        ${sample.status === 'unprocessed' ? `
                            <div class="sample-label-buttons">
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '0')" title="标签 0">0</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '1')" title="标签 1">1</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '2')" title="标签 2">2</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '3')" title="标签 3">3</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '4')" title="标签 4">4</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '5')" title="标签 5">5</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '6')" title="标签 6">6</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '7')" title="标签 7">7</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '8')" title="标签 8">8</button>
                                <button class="label-btn-small primary" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, '9')" title="标签 9">9</button>
                                <button class="label-btn-small warning" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, 'b')" title="标签 b">b</button>
                                <button class="label-btn-small danger delete-btn" onclick="event.stopPropagation(); labelSingleSample(${sample.id}, 'd')" title="删除">×</button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('') + '</div>';

            updateSelectedCount();
        }

        function toggleSelect(sampleId) {
            const sample = samples.find(s => s.id === sampleId);
            if (sample && sample.status === 'unprocessed') {
                if (selectedSamples.has(sampleId)) {
                    selectedSamples.delete(sampleId);
                } else {
                    selectedSamples.add(sampleId);
                }
                renderSamples();
            }
        }

        function selectAll() {
            selectedSamples.clear();
            samples.forEach(sample => {
                if (sample.status === 'unprocessed') {
                    selectedSamples.add(sample.id);
                }
            });
            renderSamples();
        }

        function clearSelection() {
            selectedSamples.clear();
            renderSamples();
        }

        function updateStatistics(stats) {
            // 更新选中数量
            document.getElementById('selected-count').textContent = selectedSamples.size;

            // 更新基础统计信息
            const statsElement = document.getElementById('sample-stats');
            if (statsElement && stats) {
                let statsHtml = `
                    <div style="margin-bottom: 8px;">
                        <span class="stats-count">总样本: ${stats.total}</span> |
                        <span class="stats-count">未处理: ${stats.unprocessed}</span> |
                        <span class="stats-count">已处理: ${stats.processed}</span> |
                        <span class="stats-count">已删除: ${stats.deleted}</span>
                    </div>
                `;

                // 添加标签统计
                if (stats.labels && Object.keys(stats.labels).length > 0) {
                    const labelStats = [];

                    // 按标签排序 (0-9, b)
                    const sortedLabels = Object.keys(stats.labels).sort((a, b) => {
                        if (a === 'b') return 1;
                        if (b === 'b') return -1;
                        return parseInt(a) - parseInt(b);
                    });

                    for (const label of sortedLabels) {
                        const count = stats.labels[label];
                        if (count > 0) {
                            labelStats.push(`<span class="stats-label">${label}</span><span class="stats-count">${count}</span>`);
                        }
                    }

                    if (labelStats.length > 0) {
                        statsHtml += `
                            <div style="color: #666; font-size: 13px;">
                                <strong>标签分布:</strong> ${labelStats.join(' ')}
                            </div>
                        `;
                    }
                }

                statsElement.innerHTML = statsHtml;
            }
        }

        function updateSelectedCount() {
            // 保持向后兼容的函数，用于不传入stats参数的情况
            const unprocessedCount = samples.filter(sample => sample.status === 'unprocessed').length;
            const processedCount = samples.filter(sample => sample.status === 'processed').length;
            const deletedCount = samples.filter(sample => sample.status === 'deleted').length;

            // 计算标签统计
            const labelCounts = {};
            samples.filter(sample => sample.status === 'processed').forEach(sample => {
                if (sample.label) {
                    labelCounts[sample.label] = (labelCounts[sample.label] || 0) + 1;
                }
            });

            const stats = {
                total: samples.length,
                unprocessed: unprocessedCount,
                processed: processedCount,
                deleted: deletedCount,
                labels: labelCounts
            };

            updateStatistics(stats);
        }

        async function labelSelected(label) {
            if (selectedSamples.size === 0) {
                showMessage('请先选择要标注的样本', 'error');
                return;
            }

            const confirmed = confirm(`确认将 ${selectedSamples.size} 个样本标记为 "${label}"?`);
            if (!confirmed) return;

            try {
                const response = await fetch('/api/label', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sample_ids: Array.from(selectedSamples),
                        label: label
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const successCount = data.results.filter(r => r.success).length;
                    showMessage(`成功处理 ${successCount} 个样本`, 'success');
                    selectedSamples.clear();
                    await loadSamples(); // 重新加载样本
                } else {
                    showMessage('批量标注失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        async function refreshSamples() {
            showMessage('正在重新扫描样本...', 'success');
            try {
                const response = await fetch('/api/refresh');
                const data = await response.json();

                if (data.success) {
                    samples = data.samples;
                    selectedSamples.clear();
                    renderSamples();

                    // 如果有标签统计，更新统计信息
                    if (data.label_stats) {
                        updateStatistics(data.label_stats);
                    } else {
                        updateSelectedCount(); // 回退到计算统计
                    }

                    showMessage(data.message, 'success');
                } else {
                    showMessage('刷新失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        function showMessage(message, type) {
            const container = document.getElementById('message-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            container.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSamples();
            setupDoubleClickHandler();
            setupUndoShortcut(); // 设置撤销快捷键
            setupContextMenu(); // 右键打开样本所在文件夹
        });

        // 键盘快捷键
        document.addEventListener('keypress', function(event) {
            if (selectedSamples.size > 0) {
                const key = event.key;
                if (['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b', 'd'].includes(key)) {
                    event.preventDefault();
                    labelSelected(key);
                }
            }
        });

        // 播放视频功能
        async function playVideo(sampleId) {
            try {
                console.log('开始播放视频，样本ID:', sampleId);
                showMessage('正在打开视频...', 'success');

                const response = await fetch('/api/play_video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sample_id: sampleId
                    })
                });

                console.log('API响应状态:', response.status);
                const data = await response.json();
                console.log('API响应数据:', data);

                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage('播放失败: ' + data.message, 'error');
                    console.error('播放失败详情:', data);
                }
            } catch (error) {
                console.error('播放视频出错:', error);
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 加强双击事件处理
        function setupDoubleClickHandler() {
            document.addEventListener('dblclick', function(event) {
                const sampleCard = event.target.closest('.sample-card');
                if (sampleCard) {
                    const sampleId = parseInt(sampleCard.getAttribute('data-sample-id'));
                    console.log('检测到双击事件，样本ID:', sampleId);
                    if (sampleId) {
                        event.preventDefault();
                        event.stopPropagation();
                        playVideo(sampleId);
                    }
                }
            });
        }

        // 检查撤销状态
        async function checkUndoStatus() {
            try {
                const response = await fetch('/api/can_undo');
                const data = await response.json();

                if (data.success) {
                    const undoStatus = document.getElementById('undo-status');
                    const undoInfo = document.getElementById('undo-info');

                    if (data.can_undo && data.operation_info) {
                        const info = data.operation_info;
                        undoInfo.textContent = `可以撤销 ${info.count} 个样本的标注 (标签: ${info.label})`;
                        undoStatus.style.display = 'block';
                    } else {
                        undoStatus.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('检查撤销状态失败:', error);
            }
        }

        // 撤销最近的操作
        async function undoLastOperation() {
            try {
                console.log('开始撤销操作...');
                showMessage('正在撤销...', 'success');

                const response = await fetch('/api/undo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                console.log('撤销API响应:', data);

                if (data.success) {
                    showMessage(data.message, 'success');

                    // 撤销成功后，重新选中被撤销的样本
                    if (data.undone_sample_ids && data.undone_sample_ids.length > 0) {
                        selectedSamples.clear();
                        data.undone_sample_ids.forEach(sampleId => {
                            selectedSamples.add(sampleId);
                        });
                        console.log('重新选中撤销的样本:', Array.from(selectedSamples));
                    }

                    // 重新加载样本数据（保持选中状态）
                    await loadSamples(true);

                } else {
                    showMessage('撤销失败: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('撤销操作出错:', error);
                showMessage('撤销失败: ' + error.message, 'error');
            }
        }

        // 设置Ctrl+Z快捷键
        function setupUndoShortcut() {
            document.addEventListener('keydown', function(event) {
                // 检测 Ctrl+Z (Windows/Linux) 或 Cmd+Z (Mac)
                if ((event.ctrlKey || event.metaKey) && event.key === 'z') {
                    event.preventDefault();
                    console.log('检测到Ctrl+Z快捷键');
                    undoLastOperation();
                }
            });
        }



        // 单个样本标注函数
        async function labelSingleSample(sampleId, label) {
            try {
                console.log(`内联标注单个样本: ${sampleId} -> ${label}`);

                // 直接执行标注，无需确认
                const response = await fetch('/api/label', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sample_ids: [sampleId],
                        label: label
                    })
                });

                const data = await response.json();

                if (data.success && data.results[0] && data.results[0].success) {
                    showMessage(`样本已标记为 "${label}"`, 'success');
                    await loadSamples(true); // 重新加载样本，保留选中状态
                    checkUndoStatus(); // 检查撤销状态
                } else {
                    const errorMsg = data.results && data.results[0] ? data.results[0].message : data.message;
                    showMessage('标注失败: ' + errorMsg, 'error');
                }
            } catch (error) {
                console.error('内联标注失败:', error);
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 右键菜单：打开样本所在文件夹
        function setupContextMenu() {
            document.addEventListener('contextmenu', function(event) {
                const sampleCard = event.target.closest('.sample-card');
                if (sampleCard) {
                    event.preventDefault();
                    const sampleId = parseInt(sampleCard.getAttribute('data-sample-id'));
                    if (sampleId) {
                        openSampleFolder(sampleId);
                    }
                }
            });
        }

        async function openSampleFolder(sampleId) {
            try {
                const response = await fetch('/api/open_folder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ sample_id: sampleId })
                });
                const data = await response.json();
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage('打开失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
"""

    template_file = template_dir / 'index.html'
    with open(template_file, 'w', encoding='utf-8') as f:
        f.write(html_content)


def open_browser():
    """延迟打开浏览器"""
    time.sleep(1.5)  # 等待Flask启动
    webbrowser.open('http://127.0.0.1:5000')


def main(custom_path=None):
    """主函数"""
    # 默认路径配置
    default_root_path = Path(__file__).parent / 'samples'

    print("图像标注工具 - Web版")
    print("=" * 50)
    print("🆕 新功能：骨骼朝向感知绘制")
    print("- 智能判断人体朝向（基于左肩和左脚踝位置）")
    print("- 朝右时只绘制左侧关键点，朝左时只绘制右侧关键点")
    print("- 减少视觉干扰，突出主要运动轨迹")
    print("- 自动从pkl文件提取骨骼关键点数据")
    print("- 生成彩色骨骼图片并拼接到原有3张图片下方")
    print("- 每4帧使用相同颜色，深蓝到浅蓝渐变效果")
    print("- 支持COCO 17关键点格式（YoloV8-pose输出）")
    print("=" * 50)

    # 获取样本目录路径
    if custom_path:
        root_path = Path(custom_path)
        print(f"使用指定路径: {root_path}")
    else:
        try:
            user_input = input(f"请输入样本目录路径 (默认: {default_root_path}): ").strip()
            if not user_input:
                root_path = default_root_path
                print(f"使用默认路径: {root_path}")
            else:
                root_path = Path(user_input)
                print(f"使用用户输入路径: {root_path}")
        except (EOFError, KeyboardInterrupt):
            # 处理非交互式环境或用户中断
            print(f"检测到中断，使用默认路径: {default_root_path}")
            root_path = default_root_path

    if not root_path.exists():
        print(f"路径不存在: {root_path}")
        return

    # 初始化配置
    initialize_config(root_path)

    # 创建HTML模板
    create_html_template()

    # 处理所有样本
    print("正在扫描和处理样本文件夹...")
    sample_count = process_all_samples()

    print(f"找到 {sample_count} 个样本文件夹")

    if sample_count == 0:
        print("⚠️  警告: 没有找到有效的样本文件夹")
        print("   样本文件夹应包含: 1个.pkl文件、1个.avi文件、3个.jpg文件")
    else:
        print("✅ 样本文件夹格式检查通过")
        print("📊 每个样本将生成4张图片的拼接图像：")
        print("   - 3张原始jpg图片（按文件名数字排序）")
        print("   - 1张骨骼图片（从pkl文件生成，显示所有帧的骨骼轨迹）")

    print(f"\n📁 标签目录: {CONFIG['label_pth']}")
    print(f"📝 日志文件: {CONFIG['lab_log']}")
    print("\n🚀 启动Web服务器...")
    print("🌐 服务器地址: http://127.0.0.1:5000")
    print("⏹️  按 Ctrl+C 停止服务器")

    # 创建Web应用
    app = create_web_app()

    # 在新线程中打开浏览器
    threading.Thread(target=open_browser, daemon=True).start()

    # 启动Flask应用
    try:
        app.run(host='127.0.0.1', port=5000, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")


if __name__ == "__main__":
    main()  # 不传递custom_path参数，让用户输入路径

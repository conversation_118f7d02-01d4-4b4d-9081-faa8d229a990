#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web版本的骨骼图片拼接功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from Check_imgs_Web import initialize_config, scan_all_samples, generate_sample_image

def test_web_version():
    """测试Web版本的核心功能"""
    print("测试Web版本骨骼图片拼接功能")
    print("=" * 50)
    
    # 设置样本目录
    samples_dir = Path(__file__).parent / "samples"
    
    # 初始化配置
    initialize_config(samples_dir)
    
    # 扫描样本
    sample_count = scan_all_samples()
    print(f"扫描到 {sample_count} 个样本文件夹")
    
    if sample_count > 0:
        # 找到一个有效的样本进行测试
        from Check_imgs_Web import CONFIG
        valid_sample_id = None
        for sample_id, sample in CONFIG['processed_samples'].items():
            if 'scl1' in sample['name'] or 'loop' in sample['name']:  # 找一个真实的样本
                valid_sample_id = sample_id
                break

        if not valid_sample_id:
            valid_sample_id = 2  # 使用第二个样本

        print(f"测试生成样本ID {valid_sample_id} 的拼接图片...")
        success = generate_sample_image(valid_sample_id)
        
        if success:
            print("✅ 骨骼图片拼接功能测试成功！")
            
            # 检查生成的图片
            from Check_imgs_Web import CONFIG
            if valid_sample_id in CONFIG['processed_samples']:
                sample = CONFIG['processed_samples'][valid_sample_id]
                if sample.get('concatenated_image'):
                    image_path = Path(__file__).parent / 'static' / sample['concatenated_image'].replace('processed_images/', 'processed_images/')
                    if image_path.exists():
                        from PIL import Image
                        img = Image.open(image_path)
                        print(f"生成的拼接图片尺寸: {img.width}x{img.height}")
                        print(f"图片路径: {image_path}")
                    else:
                        print("⚠️ 图片文件未找到")
        else:
            print("❌ 骨骼图片拼接功能测试失败")
    else:
        print("❌ 没有找到有效的样本文件夹")

if __name__ == "__main__":
    test_web_version()

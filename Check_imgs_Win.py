# -*-coding:utf-8-*-
"""
created By @Moss 2025.03.28
加速 图像标签的人工打标签速度 【自动化打开文件、数据】- Windows版本

新功能：
1. 脚本执行后，会依次在Windows系统的图形界面打开样本文件夹
2. 将【样本文件夹】中的所有jpg纵向拼接起来（按文件名末尾数字从小到大排序）
3. 等待用户标签输入，将整个【样本文件夹】移动到对应的标签文件夹中
4. 继续打开下一个【样本文件夹】

【样本文件夹】：包含1个pkl，1个avi，3个jpg的文件夹
"""

import os
import sys
import shutil
import subprocess
import time
import re
import platform
from pathlib import Path
from pynput import keyboard
from PIL import Image, ImageDraw
import tempfile
import pickle
import torch
import numpy as np


# COCO 17关键点连接关系定义
COCO_SKELETON_CONNECTIONS = [
    (0, 1), (0, 2), (1, 3), (2, 4),  # 头部
    (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # 上身和手臂
    (5, 11), (6, 12), (11, 12),  # 躯干
    (11, 13), (13, 15), (12, 14), (14, 16)  # 腿部
]

# COCO 17关键点分组定义（用于朝向感知绘制）
# 0:鼻子, 1:左眼, 2:右眼, 3:左耳, 4:右耳, 5:左肩, 6:右肩, 7:左肘, 8:右肘
# 9:左腕, 10:右腕, 11:左髋, 12:右髋, 13:左膝, 14:右膝, 15:左脚踝, 16:右脚踝
LEFT_KEYPOINTS = [1, 3, 5, 7, 9, 11, 13, 15]  # 左侧关键点（朝右时绘制）
RIGHT_KEYPOINTS = [2, 4, 6, 8, 10, 12, 14, 16]  # 右侧关键点（朝左时绘制）
NEUTRAL_KEYPOINTS = [0]  # 中性关键点（始终绘制）


def determine_pose_orientation(skpt_frame: list) -> str:
    """
    根据左肩点和左脚踝点的x坐标判断骨骼朝向

    Args:
        skpt_frame: 骨骼关键点帧数据列表

    Returns:
        'right' 表示朝右（左肩x < 左脚踝x），'left' 表示朝左
    """
    if not skpt_frame:
        return 'right'  # 默认朝向

    orientation_votes = []

    for frame_points in skpt_frame:
        if len(frame_points) >= 17:  # 确保有足够的关键点
            try:
                left_shoulder = frame_points[5]  # 左肩点索引5
                left_ankle = frame_points[15]    # 左脚踝点索引15

                left_shoulder_x = left_shoulder[0].item() if hasattr(left_shoulder[0], 'item') else left_shoulder[0]
                left_ankle_x = left_ankle[0].item() if hasattr(left_ankle[0], 'item') else left_ankle[0]

                # 左肩x < 左脚踝x 认为是朝右，反之朝左
                if left_shoulder_x < left_ankle_x:
                    orientation_votes.append('right')
                else:
                    orientation_votes.append('left')
            except (IndexError, AttributeError, TypeError):
                continue

    if not orientation_votes:
        return 'right'  # 默认朝向

    # 多数投票决定最终朝向
    right_votes = orientation_votes.count('right')
    left_votes = orientation_votes.count('left')

    return 'right' if right_votes >= left_votes else 'left'


def get_frame_color(frame_index: int, total_frames: int) -> tuple:
    """
    获取帧的颜色，每4帧使用相同颜色，整体从深色渐变到浅色
    """
    color_group = frame_index // 4
    max_groups = (total_frames + 3) // 4  # 向上取整

    # 从深蓝到浅蓝的渐变
    if max_groups <= 1:
        intensity = 0.8
    else:
        intensity = 0.3 + 0.7 * (color_group / (max_groups - 1))

    # RGB颜色值
    r = int(intensity * 173)
    g = int(intensity * 216)
    b = int(128 + intensity * 102)

    return (r, g, b)


def normalize_skeleton_coords(skpt_frame: list, target_width: int, target_height: int) -> list:
    """
    将骨骼坐标归一化并映射到目标画布尺寸
    """
    if not skpt_frame:
        return []

    # 合并所有帧的关键点
    all_points = torch.cat(skpt_frame, dim=0)

    # 计算边界框
    min_x, min_y = torch.min(all_points, dim=0)[0]
    max_x, max_y = torch.max(all_points, dim=0)[0]

    # 添加边距
    margin_x = (max_x - min_x) * 0.1
    margin_y = (max_y - min_y) * 0.1

    min_x -= margin_x
    max_x += margin_x
    min_y -= margin_y
    max_y += margin_y

    # 计算缩放比例，保持宽高比
    scale_x = target_width / (max_x - min_x)
    scale_y = target_height / (max_y - min_y)
    scale = min(scale_x, scale_y)

    # 计算居中偏移
    scaled_width = (max_x - min_x) * scale
    scaled_height = (max_y - min_y) * scale
    offset_x = (target_width - scaled_width) / 2
    offset_y = (target_height - scaled_height) / 2

    # 归一化所有帧的坐标
    normalized_frames = []
    for frame_points in skpt_frame:
        normalized_points = []
        for point in frame_points:
            x, y = point[0].item(), point[1].item()
            new_x = (x - min_x) * scale + offset_x
            new_y = (y - min_y) * scale + offset_y
            normalized_points.append((new_x, new_y))
        normalized_frames.append(normalized_points)

    return normalized_frames


def filter_keypoints_by_orientation(keypoints: list, orientation: str) -> list:
    """
    根据朝向过滤关键点，返回需要绘制的关键点索引

    Args:
        keypoints: 关键点列表
        orientation: 朝向 ('right' 或 'left')

    Returns:
        过滤后的关键点列表，包含(索引, 坐标)元组
    """
    if orientation == 'right':
        # 朝右时绘制左侧关键点
        target_indices = LEFT_KEYPOINTS + NEUTRAL_KEYPOINTS
    else:
        # 朝左时绘制右侧关键点
        target_indices = RIGHT_KEYPOINTS + NEUTRAL_KEYPOINTS

    filtered_keypoints = []
    for i, point in enumerate(keypoints):
        if i in target_indices:
            filtered_keypoints.append((i, point))

    return filtered_keypoints


def filter_connections_by_orientation(connections: list, orientation: str) -> list:
    """
    根据朝向过滤连接线

    Args:
        connections: 连接线列表
        orientation: 朝向 ('right' 或 'left')

    Returns:
        过滤后的连接线列表
    """
    if orientation == 'right':
        # 朝右时绘制左侧关键点相关的连接线
        target_indices = set(LEFT_KEYPOINTS + NEUTRAL_KEYPOINTS)
    else:
        # 朝左时绘制右侧关键点相关的连接线
        target_indices = set(RIGHT_KEYPOINTS + NEUTRAL_KEYPOINTS)

    filtered_connections = []
    for start_idx, end_idx in connections:
        # 只保留两个端点都在目标索引集合中的连接线
        if start_idx in target_indices and end_idx in target_indices:
            filtered_connections.append((start_idx, end_idx))

    return filtered_connections


def draw_skeleton_frame(draw: ImageDraw.Draw, keypoints: list, color: tuple, orientation: str = 'right', point_radius: int = 3, line_width: int = 2):
    """
    在图像上绘制单帧的骨骼关键点和连接线（支持朝向感知）

    Args:
        draw: PIL绘制对象
        keypoints: 关键点坐标列表
        color: 绘制颜色
        orientation: 朝向 ('right' 或 'left')
        point_radius: 关键点半径
        line_width: 连接线宽度
    """
    # 根据朝向过滤连接线
    filtered_connections = filter_connections_by_orientation(COCO_SKELETON_CONNECTIONS, orientation)

    # 绘制过滤后的连接线
    for start_idx, end_idx in filtered_connections:
        if start_idx < len(keypoints) and end_idx < len(keypoints):
            start_point = keypoints[start_idx]
            end_point = keypoints[end_idx]
            draw.line([start_point, end_point], fill=color, width=line_width)

    # 根据朝向过滤并绘制关键点
    filtered_keypoints = filter_keypoints_by_orientation(keypoints, orientation)
    for idx, point in filtered_keypoints:
        x, y = point
        draw.ellipse([x-point_radius, y-point_radius, x+point_radius, y+point_radius],
                    fill=color, outline=color)


def generate_skeleton_image(pkl_file_path: str, target_width: int, target_height: int) -> str:
    """
    从pkl文件生成骨骼图片

    Args:
        pkl_file_path: pkl文件路径
        target_width: 目标图片宽度
        target_height: 目标图片高度

    Returns:
        生成的骨骼图片路径，失败时返回None
    """
    try:
        # 加载pkl文件
        with open(pkl_file_path, 'rb') as f:
            data = pickle.load(f)

        if 'pred_skpts' not in data:
            print(f"警告: pkl文件中没有pred_skpts数据: {pkl_file_path}")
            return None

        pred_skpts = data['pred_skpts']
        if not pred_skpts:
            print(f"警告: pred_skpts数据为空: {pkl_file_path}")
            return None

        # 提取骨骼关键点数据
        skpt_frame = [skpt_ts[0][6:].view(17,3)[:,:2] for skpt_ts in pred_skpts if skpt_ts is not None]

        if not skpt_frame:
            print(f"警告: 没有有效的骨骼帧数据: {pkl_file_path}")
            return None

        # 判断骨骼朝向
        orientation = determine_pose_orientation(skpt_frame)
        print(f"检测到骨骼朝向: {'朝右' if orientation == 'right' else '朝左'}")

        # 归一化坐标
        normalized_frames = normalize_skeleton_coords(skpt_frame, target_width, target_height)

        # 创建黑色背景图像
        skeleton_image = Image.new('RGB', (target_width, target_height), 'black')
        draw = ImageDraw.Draw(skeleton_image)

        # 绘制所有帧的骨骼（支持朝向感知）
        total_frames = len(normalized_frames)
        for frame_idx, keypoints in enumerate(normalized_frames):
            color = get_frame_color(frame_idx, total_frames)
            draw_skeleton_frame(draw, keypoints, color, orientation)

        # 保存到临时文件
        temp_dir = tempfile.mkdtemp()
        skeleton_path = Path(temp_dir) / "skeleton_image.jpg"
        skeleton_image.save(skeleton_path)

        return str(skeleton_path)

    except Exception as e:
        print(f"生成骨骼图片失败: {e}")
        return None


def write_lab2txt(lab_log, sample_folder_name, lab):
    """记录标签信息到日志文件"""
    with open(lab_log, 'a+', encoding='utf-8') as f:
        f.write(f"{sample_folder_name} {lab}\n")


def wait_for_keypress(valid_keys, label=None):
    """
    等待用户按下有效按键
    """
    press_label = {'val': label}

    def on_press(key):
        # 回调函数
        try:
            if key.char in valid_keys:
                print(f'Press the Key：{key.char}')
                press_label['val'] = key.char
        except AttributeError:
            # print(f'Key {key} not in {valid_keys}.')
            pass

    listener = keyboard.Listener(on_press=on_press)
    listener.start()
    while press_label['val'] not in valid_keys:
        time.sleep(0.1)
    listener.stop()

    return press_label['val']


def extract_number_from_filename(filename):
    """
    从文件名中提取末尾的数字
    例如: "xxx-1096.jpg" -> 1096
    """
    match = re.search(r'-(\d+)\.jpg$', filename)
    return int(match.group(1)) if match else 0


def is_sample_folder(folder_path):
    """
    检查是否为样本文件夹（包含1个pkl，1个avi，3个jpg）
    """
    folder_path = Path(folder_path)
    if not folder_path.is_dir():
        return False
    
    files = list(folder_path.iterdir())
    pkl_count = len([f for f in files if f.suffix == '.pkl'])
    avi_count = len([f for f in files if f.suffix == '.avi'])
    jpg_count = len([f for f in files if f.suffix == '.jpg'])
    
    return pkl_count == 1 and avi_count == 1 and jpg_count == 3


def concatenate_images_vertically(jpg_files, output_path, skeleton_image_path=None):
    """
    将jpg文件按照文件名末尾数字从小到大纵向拼接，可选择在底部添加骨骼图片
    """
    # 按文件名末尾数字排序
    sorted_files = sorted(jpg_files, key=lambda x: extract_number_from_filename(x.name))

    # 打开所有图片
    images = []
    for jpg_file in sorted_files:
        try:
            img = Image.open(jpg_file)
            images.append(img)
        except Exception as e:
            print(f"无法打开图片 {jpg_file}: {e}")
            return None

    if not images:
        return None

    # 如果有骨骼图片，添加到列表中
    if skeleton_image_path and Path(skeleton_image_path).exists():
        try:
            skeleton_img = Image.open(skeleton_image_path)
            images.append(skeleton_img)
        except Exception as e:
            print(f"无法打开骨骼图片 {skeleton_image_path}: {e}")

    # 计算拼接后图片的尺寸
    max_width = max(img.width for img in images)
    total_height = sum(img.height for img in images)

    # 创建新的空白图片
    concatenated = Image.new('RGB', (max_width, total_height), 'white')

    # 纵向拼接图片
    current_height = 0
    for img in images:
        # 如果图片宽度小于最大宽度，居中放置
        x_offset = (max_width - img.width) // 2
        concatenated.paste(img, (x_offset, current_height))
        current_height += img.height

    # 保存拼接后的图片
    try:
        concatenated.save(output_path)
        return output_path
    except Exception as e:
        print(f"无法保存拼接图片: {e}")
        return None


def open_image_windows(image_path):
    """
    在Windows系统中打开图片
    """
    try:
        # 方法1: 使用os.startfile (Windows专用)
        if platform.system() == "Windows":
            os.startfile(str(image_path))
            return None
        else:
            # 备用方法: 使用subprocess
            if platform.system() == "Darwin":  # macOS
                return subprocess.Popen(['open', str(image_path)])
            else:  # Linux
                return subprocess.Popen(['xdg-open', str(image_path)])
    except Exception as e:
        print(f"无法打开图片: {e}")
        return None


def close_image_viewer_windows():
    """
    在Windows系统中尝试关闭图片查看器
    这个功能在Windows下比较复杂，因为不同的图片查看器程序不同
    """
    try:
        if platform.system() == "Windows":
            # Windows下可以尝试关闭常见的图片查看器
            # 这里只是示例，实际可能需要根据具体的查看器程序调整
            programs_to_close = [
                "Microsoft.Photos.exe",
                "photoviewer.dll",
                "mspaint.exe",
                "IrfanView.exe",
            ]
            
            for program in programs_to_close:
                try:
                    subprocess.run(['taskkill', '/f', '/im', program], 
                                 capture_output=True, check=False)
                except:
                    pass
    except Exception as e:
        print(f"关闭图片查看器时出现错误: {e}")


def process_sample_folder(sample_folder_path, label_pth, num_sp):
    """
    处理单个样本文件夹
    """
    sample_folder_path = Path(sample_folder_path)
    
    # 获取样本文件夹中的所有jpg文件
    jpg_files = list(sample_folder_path.glob('*.jpg'))
    
    if len(jpg_files) != 3:
        print(f"警告: {sample_folder_path.name} 中的jpg文件数量不是3个，跳过")
        return num_sp
    
    print(f"\n正在处理样本文件夹: {sample_folder_path.name}")

    # 创建临时文件用于保存拼接后的图片
    temp_dir = tempfile.mkdtemp()
    temp_image_path = Path(temp_dir) / "concatenated_image.jpg"

    # 生成骨骼图片
    pkl_files = list(sample_folder_path.glob('*.pkl'))
    skeleton_image_path = None
    if pkl_files:
        pkl_file = pkl_files[0]  # 取第一个pkl文件
        # 获取第一张jpg图片的尺寸作为骨骼图片的目标尺寸
        try:
            first_img = Image.open(jpg_files[0])
            target_width, target_height = first_img.width, first_img.height
            skeleton_image_path = generate_skeleton_image(str(pkl_file), target_width, target_height)
            if skeleton_image_path:
                print(f"骨骼图片生成成功: {skeleton_image_path}")
            else:
                print("骨骼图片生成失败，将只拼接原有3张图片")
        except Exception as e:
            print(f"生成骨骼图片时出错: {e}")

    # 拼接图片（包含骨骼图片）
    concatenated_path = concatenate_images_vertically(jpg_files, temp_image_path, skeleton_image_path)
    
    if not concatenated_path:
        print(f"无法拼接图片，跳过样本文件夹: {sample_folder_path.name}")
        return num_sp
    
    # 打开拼接后的图片
    viewer_process = None
    try:
        print(f"正在显示样本: {sample_folder_path.name}")
        viewer_process = open_image_windows(concatenated_path)
        
        print("请输入标签...")
        
        # 等待用户输入标签
        label = wait_for_keypress(valid_keys, label=None)
        
    except Exception as e:
        print(f"无法打开图片 {concatenated_path}: {e}")
        label = 'd'  # 默认删除
    
    finally:
        # 关闭图片查看器
        if viewer_process and hasattr(viewer_process, 'poll') and viewer_process.poll() is None:
            try:
                viewer_process.terminate()
                viewer_process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                viewer_process.kill()
        else:
            # Windows下尝试关闭图片查看器
            close_image_viewer_windows()
        
        # 等待一下确保图片查看器关闭
        time.sleep(1)
        
        # 清理临时文件
        try:
            if temp_image_path.exists():
                temp_image_path.unlink()
            # 清理骨骼图片临时文件
            if skeleton_image_path and Path(skeleton_image_path).exists():
                Path(skeleton_image_path).unlink()
                # 清理骨骼图片的临时目录
                skeleton_temp_dir = Path(skeleton_image_path).parent
                if skeleton_temp_dir.exists() and skeleton_temp_dir != Path(temp_dir):
                    try:
                        os.rmdir(skeleton_temp_dir)
                    except:
                        pass
            os.rmdir(temp_dir)
        except Exception as e:
            print(f"清理临时文件失败: {e}")
    
    # 根据标签处理样本文件夹
    if label in move_lab:
        # 移动整个样本文件夹到对应标签目录
        dest_folder = Path(label_pth) / label
        dest_folder.mkdir(exist_ok=True)
        
        dest_sample_folder = dest_folder / sample_folder_path.name
        
        try:
            shutil.move(str(sample_folder_path), str(dest_sample_folder))
            write_lab2txt(lab_log, sample_folder_path.name, label)
            num_sp += 1
            print(f"已将样本文件夹 {sample_folder_path.name} 移动到 {dest_folder}")
            print(f'有效样本: {num_sp}')
        except Exception as e:
            print(f"移动样本文件夹失败: {e}")
            
    elif label == 'd':
        # 删除整个样本文件夹
        try:
            shutil.rmtree(sample_folder_path)
            print(f"已删除样本文件夹 {sample_folder_path.name}")
        except Exception as e:
            print(f"删除样本文件夹失败: {e}")
    else:
        print("无效的输入，跳过该样本文件夹")
    
    return num_sp


def process_all_sample_folders(root_path, label_pth, num_sp):
    """
    递归遍历并处理所有样本文件夹
    """
    root_path = Path(root_path)
    
    # 遍历根目录下的所有子目录
    for item in root_path.rglob('*'):
        if item.is_dir() and is_sample_folder(item):
            num_sp = process_sample_folder(item, label_pth, num_sp)
    
    return num_sp


if __name__ == "__main__":
    # 检查系统类型
    print(f"检测到系统类型: {platform.system()}")
    
    if platform.system() != "Windows":
        print("警告: 此脚本是为Windows系统优化的版本")
        print("如果您在Linux/macOS系统上运行，建议使用 Check_imgs.py")
        response = input("是否继续运行? (y/n): ").lower().strip()
        if response != 'y':
            sys.exit(0)
    
    # 设置根目录路径
    print("图像标注工具 - Windows版")
    print("=" * 50)
    print("🆕 新功能：骨骼朝向感知绘制")
    print("- 智能判断人体朝向（基于左肩和左脚踝位置）")
    print("- 朝右时只绘制左侧关键点，朝左时只绘制右侧关键点")
    print("- 减少视觉干扰，突出主要运动轨迹")
    print("=" * 50)

    # 默认路径配置
    default_root_path = Path(__file__).parent / 'samples'

    # 获取样本目录路径
    try:
        user_input = input(f"请输入样本目录路径 (默认: {default_root_path}): ").strip()
        if not user_input:
            root_folder_path = default_root_path
            print(f"使用默认路径: {root_folder_path}")
        else:
            root_folder_path = Path(user_input)
            print(f"使用用户输入路径: {root_folder_path}")
    except (EOFError, KeyboardInterrupt):
        # 处理非交互式环境或用户中断
        print(f"检测到中断，使用默认路径: {default_root_path}")
        root_folder_path = default_root_path

    # 检查路径是否存在
    if not Path(root_folder_path).exists():
        print(f"路径不存在: {root_folder_path}")
        sys.exit(1)
    
    # 创建标签目录
    label_pth = Path(root_folder_path).parent / (Path(root_folder_path).stem + '_labels')
    label_pth.mkdir(exist_ok=True)
    
    # 创建日志文件
    lab_log = label_pth / 'sample_labels.txt'
    lab_log.touch(exist_ok=True)
    
    # 读取已有的标签数量
    with open(lab_log, 'r', encoding='utf-8') as f:
        num_sp = len(f.readlines())
    
    # 定义有效按键和移动标签
    valid_keys = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b', 'd'}  # 'd'是删除，其他是标签
    move_lab = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b']  # 需要移动的标签
    
    print("开始处理样本文件夹...")
    print(f"根目录: {root_folder_path}")
    print(f"标签目录: {label_pth}")
    print(f"有效按键: {valid_keys}")
    print(f"当前已有样本数: {num_sp}")
    print("\n按键说明：")
    print("0-9, b: 标签分类（会移动整个样本文件夹到对应目录）")
    print("d: 删除样本文件夹")
    print("\nWindows系统说明：")
    print("- 图片将使用系统默认程序打开")
    print("- 请在查看图片后按相应按键进行标注")
    print("- 系统会自动尝试关闭图片查看器")
    print("\n开始处理...")
    
    try:
        # 开始处理所有样本文件夹
        final_num_sp = process_all_sample_folders(root_folder_path, label_pth, num_sp)
        print(f"\n所有样本文件夹处理完成！总共处理了 {final_num_sp - num_sp} 个样本")
    except KeyboardInterrupt:
        print("\n用户中断处理")
    except Exception as e:
        print(f"\n处理过程中出现错误: {e}")
        
    print("\n按任意键退出...")
    input()

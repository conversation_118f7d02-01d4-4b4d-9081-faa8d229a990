# -*-coding:utf-8-*-
"""
created By @Moss 2025.03.28
加速 图像标签的人工打人标签速度 【自动化打开文件、数据】

在ubuntu18.04系统中，文件夹A中[文件夹中有很多子文件夹A1...An， 每个子文件夹中有若干个mp4文件， pkl文件和jpg文件]，频繁的做下面的操作：
1. 逐个打开Ai子文件夹，逐个打开子文件夹Ai中的jpg图片；
2. 对每个jpg文件打标签：例如，我认为该jpg图片为1,则将jpg文件和同名的pkl文件放到1文件夹中；
                            认为该jpg图片为0, 则删除该jpg文件和同名的pkl文件；

上述操作，主要耗时在 打开文件夹和图片文件，以及移动文件的操作；
自动化实现上述操作，脚本执行后，我只需要输入对应标签数字，按下回车键盘，即可实现上述操作；

例如 脚本执行后，会依次在ubuntu18.04的图形界面打开Ai文件夹，逐个打开jpg文件，
等待我的标签输入，例如我输入1,则将该jpg文件和同名的pkl文件移动到1文件夹，
然后继续打开下一个jpg文件，直到该Ai文件夹中的所有jpg文件都被我全部执行完成，则继续打开Ai+1文件夹，
依次循环；

"""

import os
import shutil
import subprocess
import time
from pathlib import Path
from pynput import keyboard



def write_lab2txt(lab_log, vid_info, lab):
    with open(lab_log, 'a+', encoding='utf-8') as f:
        f.write(f"{vid_info} {lab}\n")

    return


def wait_for_keypress(valid_keys, label=None):
    """
    等待用户 按下有效按键
    """
    press_label = {'val': label}

    def on_press(key):
        # 回调函数
        try:
            if key.char in valid_keys:
                print(f'Press the Key：{key.char}')
                press_label['val'] = key.char
        except AttributeError:
            # print(f'Key {key} not in {valid_keys}.')
            pass

    listener = keyboard.Listener(on_press=on_press)
    listener.start()
    while press_label['val'] not in valid_keys:
        time.sleep(0.1)
    listener.stop()

    return press_label['val']


def process_folder(folder_path, label_pth, num_sp, VideoMod=False):
    """
    处理指定文件夹中的所有子文件夹和文件
    """
    folder_path = Path(folder_path)

    # 遍历文件夹中的所有子文件夹
    for sub_folder in folder_path.iterdir():
        if sub_folder.is_dir():
            process_sub_folder(sub_folder, label_pth, num_sp, VideoMod)


def process_sub_folder(sub_folder_path, label_pth, num_sp, VideoMod=False):
    """
    处理单个子文件夹中的所有jpg文件
    """
    # 获取子文件夹中的所有jpg文件
    jpg_files = list(sub_folder_path.glob('*.jpg'))
    if not len(jpg_files):
        return

    # 打开子文件夹
    try:
        # 使用 subprocess.Popen 打开文件夹，并记录进程
        folder_process = subprocess.Popen(['xdg-open', str(sub_folder_path)])
    except Exception as e:
        print(f"无法打开文件夹 {sub_folder_path}: {e}")
        return

    # num_sp = 0
    for jpg_file in jpg_files:
        jpg_path = jpg_file
        pkl_file = jpg_path.with_suffix('.pkl')

        # 打开图片文件
        try:
            # 使用 subprocess.Popen 打开图片，并记录进程
            viewer_process = subprocess.Popen(['eog', str(jpg_path)])
        except Exception as e:
            print(f"无法打开图片 {jpg_path}: {e}")
            continue

        # 获取用户输入的标签 label = input(f"{jpg_path.name} 标签[0-3|d]: ").__str__()
        label = wait_for_keypress(valid_keys, label=None)


        # 终止图片查看器进程
        if viewer_process.poll() is None:  # 如果进程还在运行
            try:
                pid = viewer_process.pid
                os.system(f'kill {pid}')
            except subprocess.TimeoutExpired:
                # 如果超时，强行杀死进程
                viewer_process.kill()

        if label in move_lab:
            # 如果标签为1，将jpg和pkl文件 移动到1文件夹
            if VideoMod:
                dest_folder = jpg_path.parent / label
            else:
                dest_folder = Path(label_pth) / label
            dest_folder.mkdir(exist_ok=True)

            shutil.move(str(jpg_path), str(dest_folder / jpg_path.name))
            if pkl_file.exists():
                shutil.move(str(pkl_file), str(dest_folder / pkl_file.name))

            write_lab2txt(lab_log, jpg_path.name, label)
            num_sp += 1
            print(f"已将 {jpg_path.name} 和 {pkl_file.name} 移动到 {dest_folder}")
            print(f'有效样本:{num_sp}')
        elif label == 'd':
            # 如果标签为0，删除jpg和pkl文件
            jpg_path.unlink()
            if pkl_file.exists():
                pkl_file.unlink()
            print(f"已删除 {jpg_path.name} 和 {pkl_file.name}")
        else:
            print("无效的输入，跳过该文件")

    # 处理完所有图片后，关闭文件夹
    try:
        folder_name = sub_folder_path.name
        window_id = subprocess.check_output(['xdotool', 'search', '--name', folder_name]).decode().strip()
        _id = window_id.split()[0]
        subprocess.run(['xdotool', 'windowclose', _id])
    except subprocess.TimeoutExpired:
        # 如果超时，强行杀死进程
        folder_process.kill()


if __name__ == "__main__":
    # 设置文件夹A的路径:
    # TODO 要求样本 在2级子目录下
    VideoMod = True             # 是否基于视频测试集模式 处理数据
    # folder_a_path = "/media/yskj/50b3c00c-b040-4ea7-8a92-9ab4e9b50cd7/Moss_all/run50/长沙实验中学/model_Match"  # 替换为你的文件夹A的实际路径
    folder_a_path = "/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/4/长沙高新虹桥小学/0"  # 替换为你的文件夹A的实际路径
    # folder_a_path = "/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/鄂尔多斯市一中伊金霍洛分校/Run50_freeTest_freeTest_2024_Grade_PosFilted/datasets_M/model_Match/1_fake_pred"  # 替换为你的文件夹A的实际路径
    # folder_a_path = "/media/yskj/50b3c00c-b040-4ea7-8a92-9ab4e9b50cd7/Moss_all/run50/武昌区滨江实验1小/0416"  # 替换为你的文件夹A的实际路径


    label_pth = Path(folder_a_path).parent / (Path(folder_a_path).stem + '_labels')         # 默认
    label_pth.mkdir(exist_ok=True)
    lab_log = label_pth / 'name_label.txt'
    lab_log.touch(exist_ok=True)
    with open(lab_log, 'r') as f:
        num_sp = len(f.readlines())


    valid_keys = {'0','9','1','2','3', '4', 'b','d'}        # 'd'是需要删除的样本，'9'是特殊标签stack正样本, '4'借助工具作弊[动作异常]
    move_lab = ['0','9','1','2','3', '4', 'b']           # 这些标签对应的样本需要进行 移动操作, 'b' 待定情况


    # 开始处理
    process_folder(folder_a_path, label_pth, num_sp, VideoMod=VideoMod)
    print("所有文件处理完成！")
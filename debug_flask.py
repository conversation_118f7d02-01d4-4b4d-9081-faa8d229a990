#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Flask应用创建问题
"""

import sys
import traceback
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_flask_import():
    """测试Flask导入"""
    try:
        from flask import Flask, render_template, request, jsonify, send_from_directory
        print("✅ Flask导入成功")
        return True
    except Exception as e:
        print(f"❌ Flask导入失败: {e}")
        traceback.print_exc()
        return False

def test_create_web_app():
    """测试create_web_app函数"""
    try:
        # 导入Check_imgs_Web模块
        import Check_imgs_Web
        
        # 初始化一个基本的CONFIG
        Check_imgs_Web.CONFIG = {
            'root_folder_path': str(Path(__file__).parent / 'samples'),
            'label_pth': Path(__file__).parent / 'samples_labels',
            'lab_log': Path(__file__).parent / 'samples_labels' / 'sample_labels_web.txt',
            'static_images_dir': Path(__file__).parent / 'static' / 'processed_images',
            'processed_samples': {},
            'valid_keys': {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b', 'd'},
            'move_lab': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b'],
            'processing_stats': {'total': 0, 'completed': 0, 'in_progress': False},
            'last_operation': None
        }
        
        # 创建必要的目录
        Check_imgs_Web.CONFIG['label_pth'].mkdir(exist_ok=True)
        Check_imgs_Web.CONFIG['static_images_dir'].mkdir(parents=True, exist_ok=True)
        Check_imgs_Web.CONFIG['lab_log'].touch(exist_ok=True)
        
        print("✅ CONFIG初始化成功")
        
        # 测试create_web_app函数
        print("🔍 调用create_web_app()...")

        # 添加详细调试信息
        print("🔍 检查create_web_app函数是否存在...")
        if hasattr(Check_imgs_Web, 'create_web_app'):
            print("✅ create_web_app函数存在")
        else:
            print("❌ create_web_app函数不存在")
            return False

        # 尝试调用函数并捕获异常
        try:
            app = Check_imgs_Web.create_web_app()
            print(f"🔍 函数执行完成，返回值类型: {type(app)}")
            print(f"🔍 返回值内容: {app}")
        except Exception as e:
            print(f"❌ create_web_app()执行时出现异常: {e}")
            traceback.print_exc()
            return False

        if app is None:
            print("❌ create_web_app()返回了None")
            return False
        else:
            print(f"✅ create_web_app()成功返回Flask应用: {type(app)}")
            print(f"   应用名称: {app.name}")
            return True
            
    except Exception as e:
        print(f"❌ create_web_app()测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 开始调试Flask应用创建问题")
    print("=" * 50)
    
    # 测试Flask导入
    if not test_flask_import():
        return
    
    # 测试create_web_app函数
    if not test_create_web_app():
        return
    
    print("=" * 50)
    print("✅ 所有测试通过！")

if __name__ == "__main__":
    main()

# -*-coding:utf-8-*-
"""
测试撤销功能（Ctrl+Z）的脚本

测试功能：
1. 标注操作历史记录
2. Ctrl+Z 撤销最近标注
3. 撤销后样本状态恢复
4. 撤销后样本保持选中状态
5. 删除操作不被记录（不可撤销）
"""

import subprocess
import time
from pathlib import Path
import platform

def test_undo_feature():
    """测试撤销功能"""
    print("图像标注工具 - 撤销功能测试")
    print("=" * 60)
    
    # 检查系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {platform.python_version()}")
    
    # 检查当前目录
    current_dir = Path(__file__).parent
    web_script = current_dir / 'Check_imgs_Web.py'
    samples_dir = current_dir / 'samples'
    
    if not web_script.exists():
        print("❌ 找不到 Check_imgs_Web.py")
        return
    
    if not samples_dir.exists():
        print("❌ 找不到 samples 目录")
        return
    
    print("✅ 文件检查完成")
    
    # 撤销功能说明
    print("\n🔄 撤销功能特点:")
    print("1. 📝 操作记录 - 自动记录最近一次的标注操作（不包括删除）")
    print("2. ⌨️ 快捷键支持 - Ctrl+Z (Windows/Linux) 或 Cmd+Z (macOS)")
    print("3. 🖱️ 按钮操作 - 界面顶部显示撤销按钮")
    print("4. 🎯 智能选中 - 撤销后样本自动保持选中状态")
    print("5. 📁 文件还原 - 样本文件夹从标签目录移回原位置")
    print("6. 🗂️ 日志清理 - 自动从日志文件中移除撤销的记录")
    
    print("\n🧪 测试指南:")
    print("请按以下步骤测试撤销功能：")
    print()
    
    print("⭐ 基础撤销测试:")
    print("  1. 选择几个样本（单击样本卡片）")
    print("  2. 使用键盘快捷键或按钮进行标注（如标签1）")
    print("  3. 观察界面顶部是否出现蓝色撤销提示区域")
    print("  4. 按 Ctrl+Z 或点击撤销按钮")
    print("  5. 确认样本是否：")
    print("     - 重新出现在未处理列表中")
    print("     - 自动保持选中状态")
    print("     - 从标签文件夹中移回原位置")
    
    print("\n⭐ 删除操作测试:")
    print("  1. 选择一些样本")
    print("  2. 使用 'd' 键删除样本")
    print("  3. 确认撤销区域不显示（删除操作不可撤销）")
    
    print("\n⭐ 多次操作测试:")
    print("  1. 标注一批样本 → 撤销 → 重新标注为其他标签")
    print("  2. 确认只能撤销最近一次操作")
    
    print("\n⭐ 边界条件测试:")
    print("  1. 没有标注操作时按 Ctrl+Z")
    print("  2. 样本文件被外部程序移动后撤销")
    print("  3. 大量样本的撤销性能测试")
    
    print("\n📋 撤销功能的技术细节:")
    details = [
        ("后端记录", "每次标注操作都会记录样本路径、标签、时间戳"),
        ("文件操作", "使用shutil.move()在原位置和标签目录间移动"),
        ("状态同步", "样本状态从'processed'变回'unprocessed'"),
        ("日志管理", "从日志文件中精确移除对应记录"),
        ("前端同步", "撤销后自动重新加载并保持选中状态"),
        ("快捷键", "支持跨平台的Ctrl+Z和Cmd+Z组合键"),
        ("安全性", "只记录移动操作，删除操作不可撤销")
    ]
    
    for feature, desc in details:
        print(f"  {feature:10s}: {desc}")
    
    print("\n💡 使用建议:")
    print("- 标注错误时立即撤销，避免其他操作覆盖历史")
    print("- 撤销后可以重新选择标签进行正确标注")  
    print("- 删除操作请谨慎，无法撤销")
    print("- 大批量标注前建议先测试少量样本")
    
    # API接口说明
    print("\n🔗 相关API接口:")
    apis = [
        ("POST /api/label", "标注样本时自动记录操作历史"),
        ("POST /api/undo", "执行撤销操作"),
        ("GET /api/can_undo", "检查是否有可撤销的操作"),
        ("GET /api/samples", "获取最新样本状态")
    ]
    
    for endpoint, desc in apis:
        print(f"  {endpoint:20s}: {desc}")
    
    start_test = input("\n是否启动Web服务测试撤销功能? (y/n): ").lower().strip()
    
    if start_test == 'y':
        print("\n🚀 正在启动Web服务...")
        print("请在浏览器中按照测试指南进行功能验证")
        print("特别注意撤销后样本的选中状态是否正确")
        print("\n按 Ctrl+C 停止服务")
        
        try:
            # 启动Web服务
            subprocess.run(['python', str(web_script)], check=True)
        except KeyboardInterrupt:
            print("\n✅ 测试完成!")
            print("请确认撤销功能是否按预期工作")
        except subprocess.CalledProcessError as e:
            print(f"❌ 启动失败: {e}")
    else:
        print("测试取消")

def verify_undo_backend():
    """验证撤销功能的后端实现"""
    print("\n🔧 后端功能验证:")
    
    try:
        from Check_imgs_Web import CONFIG, initialize_config
        
        # 测试配置初始化
        root_path = Path(__file__).parent / 'samples'
        if root_path.exists():
            initialize_config(root_path)
            print("✅ 后端配置初始化成功")
            
            # 检查撤销相关配置
            if 'last_operation' in CONFIG:
                print("✅ 撤销历史记录配置正常")
                if CONFIG['last_operation'] is None:
                    print("✅ 初始状态正确（无可撤销操作）")
                else:
                    print(f"ℹ️  存在历史操作记录: {CONFIG['last_operation']}")
            else:
                print("❌ 缺少撤销历史记录配置")
        else:
            print("⚠️  样本目录不存在，跳过后端验证")
            
    except ImportError as e:
        print(f"❌ 后端导入失败: {e}")
    except Exception as e:
        print(f"❌ 后端验证失败: {e}")

if __name__ == "__main__":
    test_undo_feature()
    verify_undo_backend()
